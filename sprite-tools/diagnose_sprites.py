#!/usr/bin/env python3
"""
精灵图诊断工具
分析精灵图布局并生成调试信息
"""

from PIL import Image, ImageDraw, ImageFont
import os

def analyze_sprite_sheet():
    """分析当前的精灵图"""
    try:
        img = Image.open('images/fish-sheet0.png')
        print(f"🔍 分析 fish-sheet0.png")
        print(f"总尺寸: {img.size}")
        print()
        
        # 当前使用的坐标
        sprite_positions = [
            (0, 0),      (258, 0),    (516, 0),
            (0, 311),    (258, 311),  (516, 311),
            (0, 622),    (258, 622),  (516, 622)
        ]
        
        sprite_width = 258
        sprite_height = 311
        
        # 提取每个精灵并保存为单独文件
        print("📋 提取各个精灵:")
        for i, (x, y) in enumerate(sprite_positions, 1):
            try:
                # 检查坐标是否超出边界
                if x + sprite_width > img.width or y + sprite_height > img.height:
                    print(f"❌ 精灵 {i}: 坐标 ({x}, {y}) 超出图像边界")
                    continue
                
                # 提取精灵
                sprite = img.crop((x, y, x + sprite_width, y + sprite_height))
                sprite.save(f'debug_sprite_{i}.png')
                
                # 检查精灵是否为空
                bbox = sprite.getbbox()
                if bbox is None:
                    print(f"⚠️ 精灵 {i}: ({x}, {y}) - 空白区域")
                else:
                    print(f"✅ 精灵 {i}: ({x}, {y}) - 有内容")
                
            except Exception as e:
                print(f"❌ 精灵 {i}: 提取失败 - {e}")
        
        print()
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def create_debug_overlay():
    """创建带有网格和编号的调试图"""
    try:
        img = Image.open('images/fish-sheet0.png')
        debug_img = img.copy()
        draw = ImageDraw.Draw(debug_img)
        
        # 尝试加载字体
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 24)
            small_font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 16)
        except:
            font = ImageFont.load_default()
            small_font = ImageFont.load_default()
        
        sprite_positions = [
            (0, 0),      (258, 0),    (516, 0),
            (0, 311),    (258, 311),  (516, 311),
            (0, 622),    (258, 622),  (516, 622)
        ]
        
        sprite_width = 258
        sprite_height = 311
        
        # 绘制网格和标签
        for i, (x, y) in enumerate(sprite_positions, 1):
            # 绘制边框
            draw.rectangle([x, y, x + sprite_width - 1, y + sprite_height - 1], 
                         outline=(255, 0, 0, 255), width=3)
            
            # 绘制精灵编号
            draw.text((x + 5, y + 5), f"精灵 {i}", fill=(255, 255, 0, 255), font=font)
            
            # 绘制坐标
            coord_text = f"({x}, {y})"
            draw.text((x + 5, y + 35), coord_text, fill=(255, 255, 0, 255), font=small_font)
            
            # 绘制尺寸
            size_text = f"{sprite_width}×{sprite_height}"
            draw.text((x + 5, y + 55), size_text, fill=(255, 255, 0, 255), font=small_font)
        
        debug_img.save('debug_sprite_overlay.png')
        print("✅ 已创建调试叠加图: debug_sprite_overlay.png")
        
    except Exception as e:
        print(f"❌ 创建调试图失败: {e}")

def suggest_fixes():
    """根据分析结果提供修复建议"""
    print("🔧 可能的问题和解决方案:")
    print()
    print("1. 精灵位置不正确:")
    print("   - 检查你的精灵图是否按照 3×3 网格排列")
    print("   - 每个精灵应该是 258×311 像素")
    print("   - 精灵之间不应该有间隙")
    print()
    print("2. 精灵尺寸不统一:")
    print("   - 确保所有精灵都是相同尺寸")
    print("   - 可以使用图像编辑软件调整")
    print()
    print("3. 空白区域:")
    print("   - 某些位置可能没有精灵内容")
    print("   - 需要在相应位置添加精灵图像")
    print()
    print("4. 坐标超出边界:")
    print("   - 1024×1024 的画布可能不够大")
    print("   - 考虑调整精灵尺寸或布局")

def check_runtime_coordinates():
    """检查运行时文件中的坐标"""
    try:
        with open('c2runtime.js', 'r') as f:
            content = f.read()
        
        import re
        pattern = r'images/fish-sheet0\.png.*?(\d+),\s*(\d+),\s*(\d+),\s*(\d+)'
        matches = re.findall(pattern, content)
        
        print("🔍 运行时文件中的坐标:")
        for i, (x, y, w, h) in enumerate(matches, 1):
            print(f"精灵 {i}: x={x}, y={y}, width={w}, height={h}")
        
        print()
        
    except Exception as e:
        print(f"❌ 检查运行时坐标失败: {e}")

if __name__ == "__main__":
    print("🔍 开始诊断精灵图问题...")
    print("=" * 50)
    print()
    
    # 分析精灵图
    if analyze_sprite_sheet():
        print()
        
        # 创建调试图
        create_debug_overlay()
        print()
        
        # 检查运行时坐标
        check_runtime_coordinates()
        
        # 提供修复建议
        suggest_fixes()
        
        print()
        print("📁 生成的调试文件:")
        print("- debug_sprite_1.png 到 debug_sprite_9.png: 各个精灵的单独图像")
        print("- debug_sprite_overlay.png: 带网格的调试图")
        print()
        print("💡 请查看这些文件来识别问题，然后告诉我具体哪些精灵有问题。")
    else:
        print("❌ 诊断失败，请检查 fish-sheet0.png 文件是否存在。")
