#!/usr/bin/env python3
"""
精灵图布局计算器
计算在 1024x1024 的画布上可以放置多少个 258x312 的精灵
"""

def calculate_sprite_layout():
    # 画布尺寸
    canvas_width = 1024
    canvas_height = 1024
    
    # 单个精灵尺寸
    sprite_width = 258
    sprite_height = 312
    
    print("🎨 精灵图布局计算")
    print("=" * 40)
    print(f"画布尺寸: {canvas_width} × {canvas_height}")
    print(f"精灵尺寸: {sprite_width} × {sprite_height}")
    print()
    
    # 计算水平和垂直方向可以放置的精灵数量
    sprites_horizontal = canvas_width // sprite_width
    sprites_vertical = canvas_height // sprite_height
    
    print("📊 布局分析:")
    print(f"水平方向最多: {sprites_horizontal} 个精灵")
    print(f"垂直方向最多: {sprites_vertical} 个精灵")
    print(f"总计最多: {sprites_horizontal * sprites_vertical} 个精灵")
    print()
    
    # 计算实际使用的空间
    used_width = sprites_horizontal * sprite_width
    used_height = sprites_vertical * sprite_height
    
    # 计算剩余空间
    remaining_width = canvas_width - used_width
    remaining_height = canvas_height - used_height
    
    print("📏 空间使用:")
    print(f"实际使用: {used_width} × {used_height}")
    print(f"剩余空间: {remaining_width} × {remaining_height}")
    print(f"空间利用率: {(used_width * used_height) / (canvas_width * canvas_height) * 100:.1f}%")
    print()
    
    # 生成布局坐标
    print("📍 精灵位置坐标:")
    sprite_positions = []
    for row in range(sprites_vertical):
        for col in range(sprites_horizontal):
            x = col * sprite_width
            y = row * sprite_height
            sprite_positions.append((x, y))
            print(f"精灵 {len(sprite_positions):2d}: ({x:3d}, {y:3d})")
    
    print()
    print("🎯 建议:")
    if remaining_width > 0 or remaining_height > 0:
        print(f"• 可以考虑调整精灵尺寸以更好利用空间")
        print(f"• 或者在剩余空间添加其他元素")
    
    # 检查是否可以通过调整布局增加精灵
    print()
    print("🔄 其他可能的布局:")
    
    # 尝试不同的精灵尺寸
    alternative_sizes = [
        (256, 256),  # 正方形，标准尺寸
        (256, 320),  # 接近你的需求
        (260, 310),  # 稍微调整
    ]
    
    for alt_width, alt_height in alternative_sizes:
        alt_h = canvas_width // alt_width
        alt_v = canvas_height // alt_height
        alt_total = alt_h * alt_v
        print(f"• {alt_width}×{alt_height}: {alt_h}×{alt_v} = {alt_total} 个精灵")
    
    return sprite_positions

def create_layout_template():
    """创建布局模板图片"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建画布
        img = Image.new('RGBA', (1024, 1024), (240, 240, 240, 255))
        draw = ImageDraw.Draw(img)
        
        # 精灵尺寸
        sprite_width = 258
        sprite_height = 312
        
        sprites_horizontal = 1024 // sprite_width
        sprites_vertical = 1024 // sprite_height
        
        # 绘制网格和编号
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 24)
        except:
            font = ImageFont.load_default()
        
        sprite_num = 1
        for row in range(sprites_vertical):
            for col in range(sprites_horizontal):
                x = col * sprite_width
                y = row * sprite_height
                
                # 绘制边框
                draw.rectangle([x, y, x + sprite_width - 1, y + sprite_height - 1], 
                             outline=(0, 0, 0, 255), width=2)
                
                # 绘制编号
                text = f"精灵 {sprite_num}"
                text_bbox = draw.textbbox((0, 0), text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
                
                text_x = x + (sprite_width - text_width) // 2
                text_y = y + (sprite_height - text_height) // 2
                
                draw.text((text_x, text_y), text, fill=(0, 0, 0, 255), font=font)
                
                # 绘制尺寸信息
                size_text = f"{sprite_width}×{sprite_height}"
                size_bbox = draw.textbbox((0, 0), size_text, font=font)
                size_width = size_bbox[2] - size_bbox[0]
                
                size_x = x + (sprite_width - size_width) // 2
                size_y = text_y + text_height + 10
                
                draw.text((size_x, size_y), size_text, fill=(100, 100, 100, 255), font=font)
                
                sprite_num += 1
        
        # 保存模板
        img.save('sprite_layout_template.png')
        print("✅ 已创建布局模板: sprite_layout_template.png")
        
    except ImportError:
        print("⚠️ 需要 Pillow 库来创建布局模板")
    except Exception as e:
        print(f"❌ 创建布局模板时出错: {e}")

if __name__ == "__main__":
    positions = calculate_sprite_layout()
    print()
    create_layout_template()
