# 🎨 精灵图工具集

这个文件夹包含了用于调整和管理游戏精灵图的完整工具集。

## 📁 工具列表

### 🎯 主要工具

#### 1. `sprite_adjuster_fixed.html` - 精灵位置调整工具
**用途**: 可视化调整精灵在精灵图中的位置和尺寸
**使用方法**:
```bash
# 在项目根目录启动服务器
python3 -m http.server 8000

# 在浏览器中打开
http://localhost:8000/sprite-tools/sprite_adjuster_fixed.html
```
**功能**:
- 点击画布设置精灵位置
- 调整精灵尺寸
- 实时预览效果
- 生成坐标代码

#### 2. `apply_coordinates.py` - 应用坐标到游戏
**用途**: 将调整工具生成的坐标应用到游戏文件中
**使用方法**:
```bash
# 修改脚本中的坐标，然后运行
python3 sprite-tools/apply_coordinates.py
```

### 🔍 诊断工具

#### 3. `diagnose_sprites.py` - 精灵图诊断
**用途**: 分析精灵图布局，检测问题
**使用方法**:
```bash
python3 sprite-tools/diagnose_sprites.py
```

#### 4. `auto_fix_sprites.py` - 自动修复精灵坐标
**用途**: 自动检测精灵图布局并生成坐标
**使用方法**:
```bash
python3 sprite-tools/auto_fix_sprites.py
```

### 🛠️ 辅助工具

#### 5. `sprite_calculator.py` - 精灵布局计算器
**用途**: 计算不同尺寸精灵在画布上的最佳布局
**使用方法**:
```bash
python3 sprite-tools/sprite_calculator.py
```

#### 6. `fix_sprites.py` - 精灵图修复工具
**用途**: 修复精灵图尺寸问题
**使用方法**:
```bash
python3 sprite-tools/fix_sprites.py
```

#### 7. `check_verification.py` - 验证图检查工具
**用途**: 检查生成的验证图内容
**使用方法**:
```bash
python3 sprite-tools/check_verification.py
```

## 🚀 快速使用指南

### 场景1: 新精灵图需要调整坐标
```bash
# 1. 使用可视化工具调整
打开 sprite_adjuster_fixed.html

# 2. 生成坐标并应用
python3 sprite-tools/apply_coordinates.py
```

### 场景2: 精灵显示有问题
```bash
# 1. 诊断问题
python3 sprite-tools/diagnose_sprites.py

# 2. 尝试自动修复
python3 sprite-tools/auto_fix_sprites.py

# 3. 如果自动修复不理想，使用手动调整
打开 sprite_adjuster_fixed.html
```

### 场景3: 计算新的精灵布局
```bash
# 计算最佳布局
python3 sprite-tools/sprite_calculator.py
```

## 📋 依赖要求

确保安装了以下Python包:
```bash
pip3 install Pillow numpy
```

## 🔧 工具工作流程

```
精灵图问题
    ↓
诊断工具 (diagnose_sprites.py)
    ↓
自动修复 (auto_fix_sprites.py) 或 手动调整 (sprite_adjuster_fixed.html)
    ↓
应用坐标 (apply_coordinates.py)
    ↓
验证效果 (check_verification.py)
    ↓
游戏测试
```

## 💡 使用技巧

1. **备份重要**: 所有工具都会自动备份原文件到 `backup_sprites/` 文件夹
2. **缓存更新**: 应用坐标后会自动更新缓存版本，确保浏览器加载新内容
3. **验证图片**: 每次调整后都会生成验证图片，方便检查效果
4. **分步调试**: 遇到问题时，先用诊断工具分析，再选择合适的修复方案

## 📞 故障排除

### 问题: 精灵显示不正确
**解决**: 
1. 运行 `diagnose_sprites.py` 检查问题
2. 使用 `sprite_adjuster_fixed.html` 手动调整
3. 应用新坐标

### 问题: 工具运行出错
**解决**:
1. 检查是否安装了 Pillow 和 numpy
2. 确保在项目根目录运行工具
3. 检查精灵图文件是否存在

### 问题: 浏览器显示乱码
**解决**:
使用 `sprite_adjuster_fixed.html` 而不是 `sprite_adjuster.html`

## 📝 更新日志

- v1.0 - 初始版本，包含基础调整功能
- v1.1 - 添加自动诊断和修复功能
- v1.2 - 修复编码问题，优化用户界面
- v1.3 - 添加验证和预览功能
