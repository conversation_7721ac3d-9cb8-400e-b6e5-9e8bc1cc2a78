#!/usr/bin/env python3
"""
精灵图工具快速启动脚本
"""

import os
import sys
import subprocess
import webbrowser
import time

def check_dependencies():
    """检查依赖"""
    try:
        import PIL
        import numpy
        print("✅ 依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip3 install Pillow numpy")
        return False

def start_server():
    """启动HTTP服务器"""
    try:
        # 切换到项目根目录
        os.chdir('..')
        
        print("🚀 启动HTTP服务器...")
        process = subprocess.Popen(['python3', '-m', 'http.server', '8000'], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # 等待服务器启动
        time.sleep(2)
        
        print("✅ 服务器已启动在 http://localhost:8000")
        return process
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        return None

def open_adjuster_tool():
    """打开精灵调整工具"""
    try:
        url = "http://localhost:8000/sprite-tools/sprite_adjuster_fixed.html"
        webbrowser.open(url)
        print(f"🎨 已打开精灵调整工具: {url}")
    except Exception as e:
        print(f"❌ 打开工具失败: {e}")

def show_menu():
    """显示菜单"""
    print("\n" + "="*50)
    print("🎨 精灵图工具快速启动")
    print("="*50)
    print("1. 启动可视化调整工具")
    print("2. 运行精灵诊断")
    print("3. 自动修复精灵坐标")
    print("4. 查看工具说明")
    print("5. 退出")
    print("="*50)

def run_diagnostic():
    """运行诊断工具"""
    try:
        os.chdir('..')
        subprocess.run(['python3', 'sprite-tools/diagnose_sprites.py'])
    except Exception as e:
        print(f"❌ 运行诊断失败: {e}")

def run_auto_fix():
    """运行自动修复"""
    try:
        os.chdir('..')
        subprocess.run(['python3', 'sprite-tools/auto_fix_sprites.py'])
    except Exception as e:
        print(f"❌ 运行自动修复失败: {e}")

def show_help():
    """显示帮助信息"""
    print("\n📋 工具说明:")
    print("1. 可视化调整工具 - 手动调整精灵位置，最精确")
    print("2. 精灵诊断 - 分析当前精灵图问题")
    print("3. 自动修复 - 尝试自动检测和修复坐标")
    print("4. 详细说明请查看 README.md")
    print("\n💡 推荐流程:")
    print("   诊断问题 → 可视化调整 → 应用坐标")

def main():
    """主函数"""
    print("🎮 鱼类世界 - 精灵图工具集")
    
    # 检查依赖
    if not check_dependencies():
        return
    
    server_process = None
    
    try:
        while True:
            show_menu()
            choice = input("\n请选择操作 (1-5): ").strip()
            
            if choice == '1':
                if server_process is None:
                    server_process = start_server()
                    if server_process is None:
                        continue
                open_adjuster_tool()
                print("\n💡 使用完工具后，记得应用坐标:")
                print("   python3 sprite-tools/apply_coordinates.py")
                
            elif choice == '2':
                run_diagnostic()
                
            elif choice == '3':
                run_auto_fix()
                
            elif choice == '4':
                show_help()
                
            elif choice == '5':
                break
                
            else:
                print("❌ 无效选择，请重试")
            
            input("\n按回车键继续...")
    
    except KeyboardInterrupt:
        print("\n\n👋 用户中断")
    
    finally:
        # 清理服务器进程
        if server_process:
            print("🛑 关闭服务器...")
            server_process.terminate()
            server_process.wait()
        
        print("✅ 清理完成，再见！")

if __name__ == "__main__":
    main()
