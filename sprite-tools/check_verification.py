#!/usr/bin/env python3
"""
检查验证图的内容
"""

from PIL import Image
import os

def check_verification_image():
    """检查验证图的详细信息"""
    try:
        if not os.path.exists('auto_fix_verification.png'):
            print("❌ 验证图文件不存在")
            return
        
        img = Image.open('auto_fix_verification.png')
        print(f"✅ 验证图尺寸: {img.size}")
        print(f"✅ 验证图模式: {img.mode}")
        
        # 检查文件大小
        file_size = os.path.getsize('auto_fix_verification.png')
        print(f"✅ 文件大小: {file_size:,} 字节")
        
        # 创建一个简化的分析
        print("\n🔍 分析验证图内容...")
        
        # 检查是否有红色边框（验证图的特征）
        img_rgb = img.convert('RGB')
        width, height = img.size
        
        # 检查几个关键位置是否有红色
        red_pixels_found = 0
        check_positions = [
            (9, 0), (256, 0), (503, 0),  # 第一行边框
            (9, 250), (256, 250), (503, 250),  # 第二行边框
            (9, 500)  # 第三行开始
        ]
        
        for x, y in check_positions:
            if x < width and y < height:
                r, g, b = img_rgb.getpixel((x, y))
                if r > 200 and g < 100 and b < 100:  # 检测红色
                    red_pixels_found += 1
        
        print(f"🔴 检测到红色边框像素: {red_pixels_found}/{len(check_positions)}")
        
        # 创建一个更清晰的网格图
        create_clear_grid_overlay()
        
    except Exception as e:
        print(f"❌ 检查验证图失败: {e}")

def create_clear_grid_overlay():
    """创建一个更清晰的网格叠加图"""
    try:
        # 打开原始精灵图
        img = Image.open('images/fish-sheet0.png')
        
        # 创建一个半透明的叠加层
        from PIL import ImageDraw, ImageFont
        overlay = Image.new('RGBA', img.size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(overlay)
        
        # 使用检测到的坐标
        coordinates = [
            (9, 0, 247, 250),      # 精灵 1
            (256, 0, 247, 250),    # 精灵 2  
            (503, 0, 247, 250),    # 精灵 3
            (750, 0, 247, 250),    # 精灵 4
            (9, 250, 247, 250),    # 精灵 5
            (256, 250, 247, 250),  # 精灵 6
            (503, 250, 247, 250),  # 精灵 7
            (750, 250, 247, 250),  # 精灵 8
            (9, 500, 247, 250),    # 精灵 9
        ]
        
        # 加载字体
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 20)
            small_font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 14)
        except:
            font = ImageFont.load_default()
            small_font = ImageFont.load_default()
        
        # 绘制网格
        for i, (x, y, w, h) in enumerate(coordinates, 1):
            # 绘制边框 - 使用更粗的线条
            draw.rectangle([x-2, y-2, x + w + 1, y + h + 1], 
                         outline=(255, 0, 0, 255), width=4)
            
            # 绘制半透明背景
            draw.rectangle([x, y, x + w, y + h], 
                         fill=(255, 255, 0, 30))
            
            # 绘制精灵编号 - 使用黑色背景的白色文字
            text = f"精灵 {i}"
            text_bbox = draw.textbbox((0, 0), text, font=font)
            text_w = text_bbox[2] - text_bbox[0]
            text_h = text_bbox[3] - text_bbox[1]
            
            # 绘制文字背景
            draw.rectangle([x + 5, y + 5, x + 5 + text_w + 4, y + 5 + text_h + 4], 
                         fill=(0, 0, 0, 200))
            
            # 绘制文字
            draw.text((x + 7, y + 7), text, fill=(255, 255, 255, 255), font=font)
            
            # 绘制坐标信息
            coord_text = f"({x},{y})"
            draw.text((x + 7, y + 30), coord_text, fill=(255, 255, 255, 255), font=small_font)
            
            # 绘制尺寸信息
            size_text = f"{w}×{h}"
            draw.text((x + 7, y + 45), size_text, fill=(255, 255, 255, 255), font=small_font)
        
        # 合并图像
        result = Image.alpha_composite(img.convert('RGBA'), overlay)
        result.save('clear_grid_overlay.png')
        print("✅ 已创建清晰网格图: clear_grid_overlay.png")
        
        # 同时创建一个纯网格图（无背景）
        grid_only = Image.new('RGBA', img.size, (255, 255, 255, 255))
        grid_draw = ImageDraw.Draw(grid_only)
        
        for i, (x, y, w, h) in enumerate(coordinates, 1):
            # 绘制网格线
            grid_draw.rectangle([x, y, x + w - 1, y + h - 1], 
                             outline=(255, 0, 0, 255), width=2)
            
            # 绘制编号
            grid_draw.text((x + 10, y + 10), f"{i}", fill=(0, 0, 0, 255), font=font)
        
        grid_only.save('grid_layout_only.png')
        print("✅ 已创建纯网格图: grid_layout_only.png")
        
    except Exception as e:
        print(f"❌ 创建清晰网格图失败: {e}")

def extract_sprites_with_new_coords():
    """使用新坐标提取精灵"""
    try:
        img = Image.open('images/fish-sheet0.png')
        
        coordinates = [
            (9, 0, 247, 250),      # 精灵 1
            (256, 0, 247, 250),    # 精灵 2  
            (503, 0, 247, 250),    # 精灵 3
            (750, 0, 247, 250),    # 精灵 4
            (9, 250, 247, 250),    # 精灵 5
            (256, 250, 247, 250),  # 精灵 6
            (503, 250, 247, 250),  # 精灵 7
            (750, 250, 247, 250),  # 精灵 8
            (9, 500, 247, 250),    # 精灵 9
        ]
        
        print("🎯 使用新坐标提取精灵:")
        for i, (x, y, w, h) in enumerate(coordinates, 1):
            sprite = img.crop((x, y, x + w, y + h))
            sprite.save(f'new_sprite_{i}.png')
            
            # 检查精灵内容
            bbox = sprite.getbbox()
            if bbox:
                print(f"✅ 精灵 {i}: 有内容")
            else:
                print(f"⚠️ 精灵 {i}: 可能为空")
        
        print("✅ 已提取所有精灵到 new_sprite_1.png - new_sprite_9.png")
        
    except Exception as e:
        print(f"❌ 提取精灵失败: {e}")

if __name__ == "__main__":
    print("🔍 检查验证图...")
    print("=" * 40)
    
    check_verification_image()
    print()
    
    extract_sprites_with_new_coords()
    print()
    
    print("📁 生成的文件:")
    print("- clear_grid_overlay.png: 清晰的网格叠加图")
    print("- grid_layout_only.png: 纯网格布局图")
    print("- new_sprite_1.png 到 new_sprite_9.png: 使用新坐标提取的精灵")
    print()
    print("💡 请查看这些文件来验证布局是否正确")
