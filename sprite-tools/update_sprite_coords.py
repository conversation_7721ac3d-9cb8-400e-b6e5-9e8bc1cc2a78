#!/usr/bin/env python3
"""
更新精灵坐标脚本
用于更新 c2runtime.js 中的精灵坐标信息以适应新的 258x312 布局
"""

import re
import shutil

def backup_runtime():
    """备份原始的 c2runtime.js"""
    try:
        shutil.copy('c2runtime.js', 'backup_sprites/c2runtime_original.js')
        print("✅ 已备份 c2runtime.js")
    except Exception as e:
        print(f"⚠️ 备份失败: {e}")

def update_fish_coordinates():
    """更新鱼类精灵的坐标信息"""

    # 新的精灵布局 (258x311, 3x3网格)
    sprite_positions = [
        (0, 0),      (258, 0),    (516, 0),
        (0, 311),    (258, 311),  (516, 311),
        (0, 622),    (258, 622),  (516, 622)
    ]

    sprite_width = 258
    sprite_height = 311
    
    try:
        with open('c2runtime.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("🔍 查找需要更新的坐标...")
        
        # 查找 fish-sheet0.png 的引用模式
        # 原始模式: ["images/fish-sheet0.png", 419931, x, y, width, height, ...]
        pattern = r'(\["images/fish-sheet0\.png",\s*\d+,\s*)(\d+),\s*(\d+),\s*(\d+),\s*(\d+)(\s*,\s*[^\]]+\])'
        
        matches = list(re.finditer(pattern, content))
        print(f"找到 {len(matches)} 个需要更新的坐标")
        
        if len(matches) == 0:
            print("❌ 未找到需要更新的坐标模式")
            return False
        
        # 更新坐标
        updated_content = content
        sprite_index = 0
        
        for match in matches:
            if sprite_index < len(sprite_positions):
                x, y = sprite_positions[sprite_index]
                
                # 构建新的坐标字符串
                new_coords = f"{match.group(1)}{x}, {y}, {sprite_width}, {sprite_height}{match.group(6)}"
                
                # 替换原始坐标
                updated_content = updated_content.replace(match.group(0), new_coords)
                
                print(f"精灵 {sprite_index + 1}: 更新坐标为 ({x}, {y}, {sprite_width}, {sprite_height})")
                sprite_index += 1
            else:
                print(f"⚠️ 精灵数量超出布局范围，跳过精灵 {sprite_index + 1}")
                break
        
        # 保存更新后的文件
        with open('c2runtime.js', 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print(f"✅ 已更新 {sprite_index} 个精灵的坐标")
        return True
        
    except Exception as e:
        print(f"❌ 更新坐标时出错: {e}")
        return False

def create_coordinate_reference():
    """创建坐标参考文件"""
    sprite_positions = [
        (0, 0),      (258, 0),    (516, 0),
        (0, 311),    (258, 311),  (516, 311),
        (0, 622),    (258, 622),  (516, 622)
    ]
    
    try:
        with open('sprite_coordinates_reference.txt', 'w') as f:
            f.write("精灵图坐标参考 (258×311 布局)\n")
            f.write("=" * 40 + "\n\n")

            for i, (x, y) in enumerate(sprite_positions, 1):
                f.write(f"精灵 {i}: x={x}, y={y}, width=258, height=311\n")
            
            f.write("\n制作精灵图时请按照以上坐标放置你的角色图像。\n")
        
        print("✅ 已创建坐标参考文件: sprite_coordinates_reference.txt")
        
    except Exception as e:
        print(f"❌ 创建参考文件时出错: {e}")

if __name__ == "__main__":
    print("🔧 开始更新精灵坐标...")
    print()
    
    # 备份原文件
    backup_runtime()
    print()
    
    # 更新坐标
    if update_fish_coordinates():
        print()
        print("🎉 坐标更新完成！")
        print("💡 请制作新的精灵图并替换 fish-sheet0.png")
    else:
        print()
        print("❌ 坐标更新失败，请检查错误信息")
    
    print()
    
    # 创建参考文件
    create_coordinate_reference()
    
    print()
    print("📋 下一步:")
    print("1. 查看 sprite_layout_template.png 了解布局")
    print("2. 制作包含9个角色的新精灵图")
    print("3. 替换 images/fish-sheet0.png")
    print("4. 刷新游戏测试效果")
