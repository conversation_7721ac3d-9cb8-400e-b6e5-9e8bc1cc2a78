
.page_leaderboards #tabs {
	position: relative;
	height: 15%;
	width: 100%;
	margin: 0 auto;
	text-align: center;
}

	.page_leaderboards #tabs ul {
		margin: 0;
		padding: 0;
		list-style-type: none;
		position: absolute;
		width: 100%;
		left: 0;
		top: 50%;
		margin-top: -1em;
	}

	.page_leaderboards #tabs li {
		display: inline-block;
		text-transform: uppercase;
		text-align: center;
		margin: 0 2%;
	}

	.page_leaderboards #tabs a {
		display: block;
		width: 100%;
		font-size: 190%;
		line-height: 1.6em;
		text-decoration: none;
		color: #476faa;
		white-space: nowrap;
		padding: 0.1em 0.3em; 
	}

	.page_leaderboards #tabs li.active a {
		background: #9aafcc;
		color: #fff;
	}


.page_leaderboards #data {
	height: 85%;
}

	.page_leaderboards #data table {
		margin: 0;
		padding: 0;
		border: 0;
		border-collapse: collapse;
		width: 100%;
		height: 99%;
	}

	.page_leaderboards #data tr {
		background: #e6ebf1;
		font-weight: bold;
		height: 7%;
		width: 100%;
	}
	
	.page_leaderboards #data tr.spacer {
		background: #fff;
		height: 3%;
	}

		.page_leaderboards #data td {
			font-size: 1em;
			line-height: 100%;
			padding: 0;
		}
		
		.page_leaderboards #data td.position {
			width: 25%;
			text-align: center;
		}
		
		.page_leaderboards #data td.name {
			width: 40%;
			text-align: left;
		}
		
		.page_leaderboards #data td.score {
			width: 35%;
			text-align: left;
		}

		.page_leaderboards #data td:first-child {
			border-left: 10px solid #fff;
		}

		.page_leaderboards #data td:last-child {
			border-right: 10px solid #fff;
		}

	.page_leaderboards #data tr.active {
		background: #476faa;
		color: #fff;
		height: 9%;
	}
		.page_leaderboards #data tr.active td {
			font-size: 24px;
		}
		
		.page_leaderboards #data tr.active td:first-child {
			border-left-color: #476faa;
		}

		.page_leaderboards #data tr.active td:last-child {
			border-right-color: #476faa;
		}




/* Landscape */

@media screen and (orientation:landscape)
{
	
	.page_leaderboards #data td {
		font-size: 1em;
	}
	
	.page_leaderboards #data tr.active td {
		font-size: 1.3em;
	}
}

#loadingDiv{
    position:fixed;
    left:45%;
    top:50%;
    background: transparent url("../images/loading.gif");
    width: 32px;
    height: 32px;
}

/* Smartphones (portrait and landscape)  */

@media only screen 
and (min-device-width : 320px) 
and (max-device-width : 480px)
{

	.page_leaderboards #tabs a {
		font-size: 140%;
	}

	.page_leaderboards #data tr, .page_leaderboards #data tr.active {
		font-weight: normal;
		height: 9%;
	}
	
	.page_leaderboards #data tr.spacer {
		height: 1%;
	}
	
	.page_leaderboards #data tr.active td {
		font-size: 1em;
	}

	.page_leaderboards #data td:first-child {
		border-left-width: 3px;
	}

	.page_leaderboards #data td:last-child {
		border-right-width: 3px;
	}
		
}

