<!DOCTYPE html>
<html manifest="offline.appcache">
<head>
    <meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no" />
	<meta name="format-detection" content="telephone=no" />
	<meta name="msapplication-tap-highlight" content="no" />
	<meta name="theme-color" content="#000000" />
	<title>鱼类世界- MatCH3</title>
	
	<!-- Performance optimizations -->
	<meta name="renderer" content="webkit" />
	<meta name="force-rendering" content="webkit" />
	<meta http-equiv="Cache-Control" content="no-siteapp" />
	
	<!-- Standardised web app manifest -->
	<link rel="manifest" href="app.manifest" />
	
	<!-- Allow fullscreen mode on iOS -->
	<meta name="apple-mobile-web-app-capable" content="yes" />
    <!-- Status bar appearance on iOS -->
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <!-- iOS app title -->
    <meta name="apple-mobile-web-app-title" content="鱼类世界" />
	<link rel="apple-touch-icon" sizes="256x256" href="icon-256.png" />
	<meta name="HandheldFriendly" content="true" />
	
	<!-- Chrome for Android web app tags -->
	<meta name="mobile-web-app-capable" content="yes" />
	<link rel="shortcut icon" sizes="256x256" href="icon-256.png" />

    <!-- All margins and padding must be zero for the canvas to fill the screen. -->
	<style type="text/css">
		* {
			padding: 0;
			margin: 0;
		}
		html, body {
			background: #000;
			color: #fff;
			overflow: hidden;
			touch-action: none;
			-ms-touch-action: none;
		}
		canvas {
			touch-action: none;
			-ms-touch-action: none;
		}
    </style>
	
	<link href="main.css" rel="stylesheet" type="text/css" />
	<link href="page_achievements.css" rel="stylesheet" type="text/css" />
	<link href="page_leaderboards.css" rel="stylesheet" type="text/css" />
	<link href="style.css" rel="stylesheet" type="text/css" />

</head> 
 
<body> 
	<div id="fb-root"></div>
	
	<script>
	// Issue a warning if trying to preview an exported project on disk.
	(function(){
		// Check for running exported on file protocol
		if (window.location.protocol.substr(0, 4) === "file")
		{
			alert("Exported games won't work until you upload them. (When running on the file:/// protocol, browsers block many features from working for security reasons.)");
		}
	})();
	</script>
	
	<!-- The canvas must be inside a div called c2canvasdiv -->
	<div id="c2canvasdiv">
	
		<!-- The canvas the project will render to.  If you change its ID, don't forget to change the
		ID the runtime looks for in the jQuery events above (ready() and cr_sizeCanvas()). -->
		<canvas id="c2canvas" width="1920" height="1080">
			
		</canvas>
		
	</div>
	
	<!-- Pages load faster with scripts at the bottom -->
	
	<!-- Construct 2 exported games require jQuery. -->
	<script src="jquery-2.0.0.min.js"></script>

	<script src="jquery.equalheights.js"></script>

	
    <!-- The runtime script.  You can rename it, but don't forget to rename the reference here as well.
    This file will have been minified and obfuscated if you enabled "Minify script" during export. -->
	<script src="c2runtime.js"></script>

    <script>
		// Enhanced Fish World Match 3 - Performance Optimized Canvas Management
		
		// Performance monitoring
		var gameStats = {
			startTime: Date.now(),
			frameCount: 0,
			lastFpsUpdate: Date.now(),
			fps: 0
		};
		
		// Error handling
		window.addEventListener('error', function(e) {
			console.error('Game Error:', e.error);
			// Could send error to analytics here
		});
		
		// Audio context unlock for mobile devices
		var audioUnlocked = false;
		var audioContext = null;
		var audioPromptShown = false;
		
		function showAudioPrompt() {
			if (audioPromptShown || audioUnlocked) return;
			
			// Create a subtle audio prompt
			var prompt = document.createElement('div');
			prompt.id = 'audio-prompt';
			prompt.innerHTML = '🔊 点击任意位置启用音效';
			prompt.style.cssText = `
				position: fixed;
				top: 10px;
				left: 50%;
				transform: translateX(-50%);
				background: rgba(0,0,0,0.8);
				color: white;
				padding: 8px 16px;
				border-radius: 20px;
				font-size: 14px;
				z-index: 10000;
				pointer-events: none;
				opacity: 0;
				transition: opacity 0.3s ease;
			`;
			
			document.body.appendChild(prompt);
			
			// Fade in
			setTimeout(function() {
				prompt.style.opacity = '1';
			}, 100);
			
			// Auto hide after 5 seconds
			setTimeout(function() {
				if (prompt && prompt.parentNode) {
					prompt.style.opacity = '0';
					setTimeout(function() {
						if (prompt && prompt.parentNode) {
							prompt.parentNode.removeChild(prompt);
						}
					}, 300);
				}
			}, 5000);
			
			audioPromptShown = true;
		}
		
		function unlockAudio() {
			if (audioUnlocked) return;
			
			try {
				// Create audio context if it doesn't exist
				if (!audioContext) {
					audioContext = new (window.AudioContext || window.webkitAudioContext)();
				}
				
				// Resume audio context if suspended
				if (audioContext.state === 'suspended') {
					audioContext.resume().then(function() {
						console.log('Audio context resumed - 音频已启用');
						audioUnlocked = true;
						hideAudioPrompt();
					}).catch(function(e) {
						console.warn('Failed to resume audio context:', e);
					});
				} else {
					audioUnlocked = true;
					hideAudioPrompt();
				}
				
				// Remove event listeners after first interaction
				if (audioUnlocked) {
					document.removeEventListener('touchstart', unlockAudio);
					document.removeEventListener('touchend', unlockAudio);
					document.removeEventListener('mousedown', unlockAudio);
					document.removeEventListener('keydown', unlockAudio);
				}
			} catch (e) {
				console.warn('Audio unlock error:', e);
			}
		}
		
		function hideAudioPrompt() {
			var prompt = document.getElementById('audio-prompt');
			if (prompt) {
				prompt.style.opacity = '0';
				setTimeout(function() {
					if (prompt && prompt.parentNode) {
						prompt.parentNode.removeChild(prompt);
					}
				}, 300);
			}
		}
		
		// Show audio prompt after a short delay
		setTimeout(showAudioPrompt, 2000);
		
		// Add audio unlock listeners
		document.addEventListener('touchstart', unlockAudio, false);
		document.addEventListener('touchend', unlockAudio, false);
		document.addEventListener('mousedown', unlockAudio, false);
		document.addEventListener('keydown', unlockAudio, false);
		
		// Enhanced canvas resize with performance optimization
		function resizeCanvas() {
			try {
				var canvas = document.getElementById("c2canvas");
				if (!canvas) return;
				
				// Get viewport dimensions
				var viewportWidth = window.innerWidth;
				var viewportHeight = window.innerHeight;
				
				// Get canvas original dimensions
				var canvasWidth = canvas.width || 1920;
				var canvasHeight = canvas.height || 1080;
				
				// Calculate scaling ratio to fit viewport while maintaining aspect ratio
				var scaleX = viewportWidth / canvasWidth;
				var scaleY = viewportHeight / canvasHeight;
				var scale = Math.min(scaleX, scaleY);
				
				// Calculate final display dimensions
				var displayWidth = Math.floor(canvasWidth * scale);
				var displayHeight = Math.floor(canvasHeight * scale);
				
				// Apply dimensions and centering
				canvas.style.width = displayWidth + "px";
				canvas.style.height = displayHeight + "px";
				canvas.style.position = "fixed";
				canvas.style.left = "50%";
				canvas.style.top = "50%";
				canvas.style.transform = "translate(-50%, -50%)";
				canvas.style.zIndex = "1";
				
				// Ensure canvas container is properly positioned
				var canvasDiv = document.getElementById("c2canvasdiv");
				if (canvasDiv) {
					canvasDiv.style.position = "fixed";
					canvasDiv.style.left = "0";
					canvasDiv.style.top = "0";
					canvasDiv.style.width = "100%";
					canvasDiv.style.height = "100%";
					canvasDiv.style.overflow = "hidden";
				}
				
				console.log('Canvas resized:', displayWidth + 'x' + displayHeight, 'Scale:', scale.toFixed(2));
				
			} catch (e) {
				console.error('Canvas resize error:', e);
			}
		}
		
		// Optimized event handlers
		var resizeTimeout;
		function handleResize() {
			clearTimeout(resizeTimeout);
			resizeTimeout = setTimeout(resizeCanvas, 100); // Debounce resize
		}
		
		// FPS monitoring
		function updateFPS() {
			gameStats.frameCount++;
			var now = Date.now();
			if (now - gameStats.lastFpsUpdate >= 1000) {
				gameStats.fps = Math.round((gameStats.frameCount * 1000) / (now - gameStats.lastFpsUpdate));
				gameStats.frameCount = 0;
				gameStats.lastFpsUpdate = now;
				// Optional: Display FPS in console for debugging
				// console.log('FPS:', gameStats.fps);
			}
			requestAnimationFrame(updateFPS);
		}
		
		// Size the canvas to fill the browser viewport.
		jQuery(window).resize(function() {
			cr_sizeCanvas(jQuery(window).width(), jQuery(window).height());
			handleResize();
		});
		
		// Start the Construct 2 project running on window load.
		jQuery(document).ready(function ()
		{			
			try {
				// Hide address bar on mobile
				setTimeout(function() {
					window.scrollTo(0, 1);
				}, 100);
				
				// Initialize canvas
				resizeCanvas();
				
				// Start FPS monitoring
				updateFPS();
				
				// Enhanced touch handling for mobile
				document.addEventListener("touchmove", function(e) {
					// Only prevent default for game area
					if (e.target.tagName === 'CANVAS') {
						e.preventDefault();
					}
				}, { passive: false });
				
				// Prevent context menu on long press
				document.addEventListener("contextmenu", function(e) {
					e.preventDefault();
				}, false);
				
				// Prevent middle-click scrolling
				document.addEventListener("mousedown", function(e) {
					if (e.button === 1) e.preventDefault();
				}, false);
				
			} catch (e) {
				console.error('Initialization error:', e);
			}
			
			// Create new runtime using the c2canvas
			cr_createRuntime("c2canvas");
		});
		
		// Enhanced visibility handling with debouncing to prevent rapid suspend/resume
		var isGameSuspended = false;
		var suspendTimeout = null;
		var resumeTimeout = null;
		var SUSPEND_DELAY = 100; // ms delay before suspending
		var RESUME_DELAY = 50;   // ms delay before resuming
		
		function suspendGame() {
			if (isGameSuspended) return;
			
			try {
				if (typeof cr_setSuspended === 'function') {
					cr_setSuspended(true);
					isGameSuspended = true;
					console.log('Game suspended');
				}
			} catch (e) {
				console.error('Suspend error:', e);
			}
		}
		
		function resumeGame() {
			if (!isGameSuspended) return;
			
			try {
				if (typeof cr_setSuspended === 'function') {
					cr_setSuspended(false);
					isGameSuspended = false;
					console.log('Game resumed');
				}
			} catch (e) {
				console.error('Resume error:', e);
			}
		}
		
		function onVisibilityChanged() {
			try {
				var hidden = document.hidden || document.mozHidden || 
							 document.webkitHidden || document.msHidden;
				
				// Clear existing timeouts
				if (suspendTimeout) {
					clearTimeout(suspendTimeout);
					suspendTimeout = null;
				}
				if (resumeTimeout) {
					clearTimeout(resumeTimeout);
					resumeTimeout = null;
				}
				
				if (hidden && !isGameSuspended) {
					// Delay suspension to avoid rapid toggling
					suspendTimeout = setTimeout(suspendGame, SUSPEND_DELAY);
				} else if (!hidden && isGameSuspended) {
					// Delay resumption to ensure stability
					resumeTimeout = setTimeout(resumeGame, RESUME_DELAY);
				}
			} catch (e) {
				console.error('Visibility change error:', e);
			}
		}
		
		// Cross-browser visibility API
		var visibilityEvents = [
			"visibilitychange", "mozvisibilitychange", 
			"webkitvisibilitychange", "msvisibilitychange"
		];
		
		visibilityEvents.forEach(function(event) {
			document.addEventListener(event, onVisibilityChanged, false);
		});
		
		// Page lifecycle events
		window.addEventListener("pageshow", function (event) {
			if (event.persisted && isGameSuspended) {
				cr_setSuspended(false);
				isGameSuspended = false;
			}
		}, false);
		
		window.addEventListener("pagehide", function (event) {
			if (!isGameSuspended) {
				cr_setSuspended(true);
				isGameSuspended = true;
			}
		}, false);
		
		// Focus events with debouncing
		var focusTimeout = null;
		var blurTimeout = null;
		
		window.addEventListener("focus", function (event) {
			if (blurTimeout) {
				clearTimeout(blurTimeout);
				blurTimeout = null;
			}
			
			if (isGameSuspended) {
				focusTimeout = setTimeout(function() {
					resumeGame();
				}, RESUME_DELAY);
			}
		}, false);
		
		window.addEventListener("blur", function (event) {
			if (focusTimeout) {
				clearTimeout(focusTimeout);
				focusTimeout = null;
			}
			
			if (!isGameSuspended) {
				blurTimeout = setTimeout(function() {
					suspendGame();
				}, SUSPEND_DELAY);
			}
		}, false);
		
		// Memory management
		window.addEventListener("beforeunload", function() {
			// Clean up resources
			if (typeof cr_setSuspended === 'function') {
				cr_setSuspended(true);
			}
		});
		
    </script>
</body> 
</html>