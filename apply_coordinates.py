#!/usr/bin/env python3
"""
应用手动调整的精灵坐标
从调整工具生成的坐标更新游戏文件
"""

import re
import shutil
import os

def apply_custom_coordinates(coordinates):
    """应用自定义坐标到游戏文件"""
    try:
        # 备份原文件
        if not os.path.exists('backup_sprites'):
            os.makedirs('backup_sprites')
        
        shutil.copy('c2runtime.js', f'backup_sprites/c2runtime_before_manual_fix.js')
        print("✅ 已备份 c2runtime.js")
        
        with open('c2runtime.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 找到所有fish-sheet0.png的引用
        pattern = r'(\["images/fish-sheet0\.png",\s*\d+,\s*)(\d+),\s*(\d+),\s*(\d+),\s*(\d+)(\s*,\s*[^\]]+\])'
        matches = list(re.finditer(pattern, content))
        
        print(f"🔍 找到 {len(matches)} 个需要更新的坐标")
        
        if len(matches) == 0:
            print("❌ 未找到需要更新的坐标")
            return False
        
        # 更新坐标
        updated_content = content
        for i, match in enumerate(matches):
            if i < len(coordinates):
                x, y, w, h = coordinates[i]
                new_coords = f"{match.group(1)}{x}, {y}, {w}, {h}{match.group(6)}"
                updated_content = updated_content.replace(match.group(0), new_coords)
                print(f"✅ 更新精灵 {i+1}: ({x}, {y}, {w}, {h})")
            else:
                print(f"⚠️ 精灵 {i+1}: 超出提供的坐标范围")
        
        # 保存更新后的文件
        with open('c2runtime.js', 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print("✅ 运行时文件已更新")
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False

def update_cache_version():
    """更新缓存版本"""
    try:
        with open('offline.appcache', 'r') as f:
            content = f.read()
        
        # 更新版本号
        import time
        timestamp = int(time.time())
        
        # 查找版本行并更新
        version_pattern = r'# Version: ([\d\.]+)'
        match = re.search(version_pattern, content)
        
        if match:
            old_version = match.group(1)
            # 增加版本号
            version_parts = old_version.split('.')
            version_parts[-1] = str(int(version_parts[-1]) + 1)
            new_version = '.'.join(version_parts)
        else:
            new_version = "1.0.8"
        
        # 更新内容
        updated_content = re.sub(
            r'# Version: [\d\.]+\n# Last Updated: [^\n]+',
            f'# Version: {new_version}\n# Last Updated: 2024-12-21 - Manual sprite adjustment',
            content
        )
        
        with open('offline.appcache', 'w') as f:
            f.write(updated_content)
        
        print(f"✅ 缓存版本已更新到 {new_version}")
        return True
        
    except Exception as e:
        print(f"❌ 更新缓存版本失败: {e}")
        return False

def create_verification_image(coordinates):
    """创建验证图像"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 打开原始精灵图
        img = Image.open('images/fish-sheet0.png')
        verify_img = img.copy()
        draw = ImageDraw.Draw(verify_img)
        
        # 加载字体
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 20)
        except:
            font = ImageFont.load_default()
        
        # 绘制每个精灵的边框和标签
        for i, (x, y, w, h) in enumerate(coordinates, 1):
            # 绘制边框
            draw.rectangle([x, y, x + w - 1, y + h - 1], 
                         outline=(255, 0, 0, 255), width=3)
            
            # 绘制半透明背景
            draw.rectangle([x, y, x + w, y + h], 
                         fill=(255, 255, 0, 30))
            
            # 绘制精灵编号
            draw.rectangle([x + 5, y + 5, x + 35, y + 25], 
                         fill=(0, 0, 0, 200))
            draw.text((x + 8, y + 8), f"{i}", fill=(255, 255, 255, 255), font=font)
        
        verify_img.save('manual_adjustment_verification.png')
        print("✅ 已创建验证图: manual_adjustment_verification.png")
        
    except Exception as e:
        print(f"❌ 创建验证图失败: {e}")

def extract_sprites_for_preview(coordinates):
    """提取精灵用于预览"""
    try:
        from PIL import Image
        
        img = Image.open('images/fish-sheet0.png')
        
        for i, (x, y, w, h) in enumerate(coordinates, 1):
            sprite = img.crop((x, y, x + w, y + h))
            sprite.save(f'manual_sprite_{i}.png')
        
        print("✅ 已提取所有精灵到 manual_sprite_1.png - manual_sprite_9.png")
        
    except Exception as e:
        print(f"❌ 提取精灵失败: {e}")

# 手动调整后的坐标
MANUAL_COORDINATES = [
    (1, 0, 258, 311),      # 精灵 1
    (381, 1, 258, 311),    # 精灵 2
    (747, 3, 258, 311),    # 精灵 3
    (30, 358, 258, 311),   # 精灵 4
    (388, 360, 258, 311),  # 精灵 5
    (732, 356, 258, 311),  # 精灵 6
    (33, 683, 258, 311),   # 精灵 7
    (365, 684, 258, 311),  # 精灵 8
    (720, 692, 258, 311),  # 精灵 9
]

if __name__ == "__main__":
    print("🔧 应用手动调整的精灵坐标...")
    print("=" * 50)
    print()
    
    print("📋 使用说明:")
    print("1. 打开 sprite_adjuster.html 调整精灵位置")
    print("2. 点击'生成坐标代码'获取坐标")
    print("3. 将坐标复制到此脚本的 coordinates 变量中")
    print("4. 运行此脚本应用更改")
    print()
    
    # 使用手动调整后的坐标
    coordinates = MANUAL_COORDINATES
    
    print("🎯 当前使用的坐标:")
    for i, (x, y, w, h) in enumerate(coordinates, 1):
        print(f"精灵 {i}: ({x}, {y}, {w}, {h})")
    print()
    
    # 询问是否继续
    response = input("是否使用这些坐标更新游戏? (y/n): ").strip().lower()
    
    if response == 'y':
        # 应用坐标
        if apply_custom_coordinates(coordinates):
            # 更新缓存
            update_cache_version()
            
            # 创建验证图
            create_verification_image(coordinates)
            
            # 提取精灵预览
            extract_sprites_for_preview(coordinates)
            
            print()
            print("🎉 手动调整完成!")
            print("📁 生成的文件:")
            print("- manual_adjustment_verification.png: 验证图")
            print("- manual_sprite_1.png 到 manual_sprite_9.png: 精灵预览")
            print()
            print("💡 现在可以刷新游戏测试效果了!")
        else:
            print("❌ 应用坐标失败")
    else:
        print("❌ 操作已取消")
        print("💡 请先使用 sprite_adjuster.html 调整坐标，然后更新此脚本中的 coordinates 变量")
