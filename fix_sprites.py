#!/usr/bin/env python3
"""
精灵图修复脚本
用于调整鱼类精灵图的尺寸和布局
"""

from PIL import Image
import os

def fix_fish_sheet1():
    """修复 fish-sheet1.png 的尺寸"""
    try:
        # 打开原图
        img = Image.open('images/fish-sheet1.png')
        print(f"原始 fish-sheet1.png 尺寸: {img.size}")
        
        # 创建 258x258 的透明背景
        new_img = Image.new('RGBA', (258, 258), (0, 0, 0, 0))
        
        # 计算居中位置
        x = (258 - img.width) // 2
        y = (258 - img.height) // 2
        
        # 将原图粘贴到中心
        new_img.paste(img, (x, y), img if img.mode == 'RGBA' else None)
        
        # 备份原文件
        if not os.path.exists('backup_sprites'):
            os.makedirs('backup_sprites')
        img.save('backup_sprites/fish-sheet1_original.png')
        
        # 保存修复后的文件
        new_img.save('images/fish-sheet1.png')
        print(f"✅ fish-sheet1.png 已修复为 258x258")
        
    except Exception as e:
        print(f"❌ 修复 fish-sheet1.png 时出错: {e}")

def analyze_fish_sheet0():
    """分析 fish-sheet0.png 的布局"""
    try:
        img = Image.open('images/fish-sheet0.png')
        print(f"fish-sheet0.png 尺寸: {img.size}")
        
        # 检查是否是标准的 1024x1024
        if img.size == (1024, 1024):
            print("✅ fish-sheet0.png 尺寸正确")
            
            # 分析网格布局
            grid_size = 1024 // 4  # 应该是 256x256 每个格子
            print(f"预期每个格子尺寸: {grid_size}x{grid_size}")
            
            # 提取第一个格子作为示例
            first_cell = img.crop((0, 0, grid_size, grid_size))
            first_cell.save('debug_first_cell.png')
            print("✅ 已保存第一个格子为 debug_first_cell.png")
            
        else:
            print("⚠️ fish-sheet0.png 尺寸不是标准的 1024x1024")
            
    except Exception as e:
        print(f"❌ 分析 fish-sheet0.png 时出错: {e}")

def create_test_sprite_sheet():
    """创建一个测试用的精灵图，帮助理解布局"""
    try:
        # 创建 1024x1024 的测试图
        test_img = Image.new('RGBA', (1024, 1024), (255, 255, 255, 0))
        
        # 绘制网格线
        from PIL import ImageDraw
        draw = ImageDraw.Draw(test_img)
        
        # 绘制 4x4 网格
        for i in range(5):
            x = i * 256
            draw.line([(x, 0), (x, 1024)], fill=(255, 0, 0, 128), width=2)
            draw.line([(0, x), (1024, x)], fill=(255, 0, 0, 128), width=2)
        
        # 在每个格子中添加编号
        from PIL import ImageFont
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 36)
        except:
            font = ImageFont.load_default()
        
        for row in range(4):
            for col in range(4):
                x = col * 256 + 128
                y = row * 256 + 128
                text = f"{row * 4 + col + 1}"
                draw.text((x, y), text, fill=(0, 0, 0, 255), font=font, anchor="mm")
        
        test_img.save('test_sprite_layout.png')
        print("✅ 已创建测试精灵图布局: test_sprite_layout.png")
        
    except Exception as e:
        print(f"❌ 创建测试精灵图时出错: {e}")

if __name__ == "__main__":
    print("🔧 开始修复精灵图...")
    print()
    
    # 修复 fish-sheet1.png
    fix_fish_sheet1()
    print()
    
    # 分析 fish-sheet0.png
    analyze_fish_sheet0()
    print()
    
    # 创建测试布局
    create_test_sprite_sheet()
    print()
    
    print("🎉 修复完成！请刷新游戏页面查看效果。")
    print("💡 如果还有问题，请查看生成的测试文件来理解正确的布局。")
