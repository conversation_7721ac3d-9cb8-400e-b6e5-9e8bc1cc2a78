#!/usr/bin/env python3
"""
修复 fish-sheet1.png 的尺寸
将 258x313 调整为 258x258 以匹配游戏代码
"""

from PIL import Image
import os

def fix_fish_sheet1_size():
    """修复 fish-sheet1.png 的尺寸"""
    try:
        # 打开原图
        img = Image.open('images/fish-sheet1.png')
        print(f"原始 fish-sheet1.png 尺寸: {img.size}")
        
        # 备份原文件
        if not os.path.exists('backup_sprites'):
            os.makedirs('backup_sprites')
        img.save('backup_sprites/fish-sheet1_313.png')
        print("✅ 已备份原始文件")
        
        # 方案1: 裁剪到 258x258 (从顶部裁剪)
        if img.height > 258:
            # 从中心裁剪
            crop_top = (img.height - 258) // 2
            cropped_img = img.crop((0, crop_top, 258, crop_top + 258))
            cropped_img.save('images/fish-sheet1_cropped.png')
            print(f"✅ 已创建裁剪版本: fish-sheet1_cropped.png (258x258)")
        
        # 方案2: 缩放到 258x258
        scaled_img = img.resize((258, 258), Image.Resampling.LANCZOS)
        scaled_img.save('images/fish-sheet1_scaled.png')
        print(f"✅ 已创建缩放版本: fish-sheet1_scaled.png (258x258)")
        
        # 方案3: 添加透明边距到 258x258
        padded_img = Image.new('RGBA', (258, 258), (0, 0, 0, 0))
        
        # 计算居中位置
        x = (258 - img.width) // 2
        y = (258 - img.height) // 2
        
        # 如果图片太高，从顶部开始放置
        if img.height > 258:
            y = 0
            # 裁剪图片到合适高度
            img_to_paste = img.crop((0, 0, img.width, 258))
        else:
            img_to_paste = img
        
        padded_img.paste(img_to_paste, (x, y), img_to_paste if img_to_paste.mode == 'RGBA' else None)
        padded_img.save('images/fish-sheet1_padded.png')
        print(f"✅ 已创建填充版本: fish-sheet1_padded.png (258x258)")
        
        print()
        print("📋 请选择一个版本替换原文件:")
        print("1. fish-sheet1_cropped.png - 裁剪版本 (保持原始质量)")
        print("2. fish-sheet1_scaled.png - 缩放版本 (可能略微变形)")
        print("3. fish-sheet1_padded.png - 填充版本 (保持比例，添加透明边距)")
        print()
        print("建议使用裁剪版本 (选项1)")
        
    except Exception as e:
        print(f"❌ 处理 fish-sheet1.png 时出错: {e}")

def apply_fix(choice):
    """应用选择的修复方案"""
    try:
        if choice == "1":
            source = 'images/fish-sheet1_cropped.png'
        elif choice == "2":
            source = 'images/fish-sheet1_scaled.png'
        elif choice == "3":
            source = 'images/fish-sheet1_padded.png'
        else:
            print("❌ 无效选择")
            return False
        
        if os.path.exists(source):
            # 替换原文件
            import shutil
            shutil.copy(source, 'images/fish-sheet1.png')
            print(f"✅ 已应用修复方案，fish-sheet1.png 现在是 258x258")
            
            # 清理临时文件
            for temp_file in ['images/fish-sheet1_cropped.png', 'images/fish-sheet1_scaled.png', 'images/fish-sheet1_padded.png']:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            
            return True
        else:
            print(f"❌ 找不到文件: {source}")
            return False
            
    except Exception as e:
        print(f"❌ 应用修复时出错: {e}")
        return False

if __name__ == "__main__":
    print("🔧 修复 fish-sheet1.png 尺寸...")
    print()
    
    fix_fish_sheet1_size()
    
    print()
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if apply_fix(choice):
        print()
        print("🎉 修复完成！")
        print("💡 现在可以刷新游戏测试效果了")
    else:
        print()
        print("❌ 修复失败，请手动处理")
