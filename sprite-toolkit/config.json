{"version": "1.0.0", "name": "Sprite Toolkit", "description": "Universal sprite sheet coordinate editor", "server": {"default_port": 8080, "fallback_ports": [8081, 8082, 8083, 5000, 3000], "host": "localhost", "auto_open_browser": true}, "ui": {"default_language": "en", "theme": "default", "auto_save": true, "grid_size": 50, "zoom_levels": [0.1, 0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 2.0, 3.0, 5.0]}, "engines": {"supported": [{"id": "construct2", "name": "Construct 2/3", "detection_files": ["c2runtime.js"], "coordinate_pattern": "\\[\"([^\"]+\\.(?:png|jpg|jpeg|gif|bmp))\",\\s*\\d+,\\s*(\\d+),\\s*(\\d+),\\s*(\\d+),\\s*(\\d+)"}, {"id": "godot", "name": "<PERSON><PERSON>", "detection_files": ["project.godot"], "coordinate_pattern": null}, {"id": "unity", "name": "Unity", "detection_files": [".unity", "Assets/"], "coordinate_pattern": null}, {"id": "web", "name": "Web Game", "detection_files": ["index.html", "main.html", "game.html"], "coordinate_pattern": null}, {"id": "generic", "name": "Generic", "detection_files": [], "coordinate_pattern": null}]}, "export": {"formats": [{"id": "python", "name": "Python Code", "extension": ".py", "icon": "🐍"}, {"id": "json", "name": "JSON Config", "extension": ".json", "icon": "📄"}, {"id": "css", "name": "CSS Sprites", "extension": ".css", "icon": "🎨"}, {"id": "javascript", "name": "JavaScript", "extension": ".js", "icon": "📜"}]}, "backup": {"enabled": true, "max_backups": 10, "auto_cleanup": true, "timestamp_format": "%Y%m%d_%H%M%S"}, "logging": {"level": "INFO", "file": "sprite-toolkit.log", "max_size": "10MB", "backup_count": 5}, "features": {"auto_detect": true, "manual_mode": true, "grid_mode": true, "verification_image": false, "batch_processing": false, "plugin_system": false}}