#!/usr/bin/env python3
"""
🌐 精灵图工具包Web服务器
提供RESTful API和Web界面
"""

import os
import json
import base64
import time
from io import BytesIO
from flask import Flask, request, jsonify, send_from_directory, render_template_string
from flask_cors import CORS
from PIL import Image, ImageDraw, ImageFont
from toolkit import SpriteToolkit, SpriteInfo

app = Flask(__name__)
CORS(app)

# 全局工具包实例
toolkit = None

@app.route('/')
def index():
    """主页面"""
    return send_from_directory('web', 'index.html')

@app.route('/web/<path:filename>')
def web_files(filename):
    """静态文件服务"""
    return send_from_directory('web', filename)

@app.route('/api/init', methods=['POST'])
def init_project():
    """初始化项目"""
    global toolkit
    
    data = request.get_json()
    project_path = data.get('project_path', '..')
    
    try:
        toolkit = SpriteToolkit(project_path)
        
        return jsonify({
            'success': True,
            'config': {
                'engine': toolkit.config.engine,
                'sprite_sheet_path': toolkit.config.sprite_sheet_path,
                'runtime_file': toolkit.config.runtime_file
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/load_sprite_sheet', methods=['POST'])
def load_sprite_sheet():
    """加载精灵图"""
    if not toolkit:
        return jsonify({'success': False, 'error': '请先初始化项目'})
    
    data = request.get_json()
    sprite_path = data.get('sprite_path')
    
    success = toolkit.load_sprite_sheet(sprite_path)
    
    if success:
        # 返回精灵图的base64编码
        img_buffer = BytesIO()
        toolkit.sprite_image.save(img_buffer, format='PNG')
        img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
        
        return jsonify({
            'success': True,
            'image_data': f'data:image/png;base64,{img_base64}',
            'size': toolkit.sprite_image.size
        })
    else:
        return jsonify({'success': False, 'error': '加载精灵图失败'})

@app.route('/api/analyze_sprites', methods=['POST'])
def analyze_sprites():
    """分析精灵图"""
    if not toolkit:
        return jsonify({'success': False, 'error': '请先初始化项目'})
    
    data = request.get_json()
    auto_detect = data.get('auto_detect', True)
    
    sprites = toolkit.analyze_sprites(auto_detect)
    
    return jsonify({
        'success': True,
        'sprites': [sprite.to_dict() for sprite in sprites]
    })

@app.route('/api/update_sprites', methods=['POST'])
def update_sprites():
    """更新精灵坐标"""
    if not toolkit:
        return jsonify({'success': False, 'error': '请先初始化项目'})
    
    data = request.get_json()
    sprites_data = data.get('sprites', [])
    
    # 转换为SpriteInfo对象
    sprites = []
    for sprite_data in sprites_data:
        sprites.append(SpriteInfo(
            id=sprite_data['id'],
            x=sprite_data['x'],
            y=sprite_data['y'],
            width=sprite_data['width'],
            height=sprite_data['height'],
            name=sprite_data.get('name', f"Sprite {sprite_data['id'] + 1}")
        ))
    
    success = toolkit.update_sprites(sprites)
    
    return jsonify({'success': success})

@app.route('/api/generate_verification', methods=['POST'])
def generate_verification():
    """生成验证图"""
    if not toolkit or not toolkit.sprite_image:
        return jsonify({'success': False, 'error': '请先加载精灵图'})
    
    try:
        # 创建验证图
        verify_img = toolkit.sprite_image.copy()
        draw = ImageDraw.Draw(verify_img)
        
        # 绘制精灵边框
        for sprite in toolkit.sprites:
            # 绘制边框
            draw.rectangle([sprite.x, sprite.y, sprite.x + sprite.width - 1, sprite.y + sprite.height - 1], 
                         outline=(255, 0, 0, 255), width=3)
            
            # 绘制半透明背景
            overlay = Image.new('RGBA', verify_img.size, (255, 255, 0, 30))
            mask = Image.new('RGBA', verify_img.size, (0, 0, 0, 0))
            mask_draw = ImageDraw.Draw(mask)
            mask_draw.rectangle([sprite.x, sprite.y, sprite.x + sprite.width, sprite.y + sprite.height], 
                              fill=(255, 255, 255, 255))
            verify_img = Image.alpha_composite(verify_img.convert('RGBA'), overlay)
            
            # 绘制精灵编号
            try:
                font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 20)
            except:
                font = ImageFont.load_default()
            
            draw.rectangle([sprite.x + 5, sprite.y + 5, sprite.x + 35, sprite.y + 25], 
                         fill=(0, 0, 0, 200))
            draw.text((sprite.x + 8, sprite.y + 8), f"{sprite.id + 1}", 
                     fill=(255, 255, 255, 255), font=font)
        
        # 转换为base64
        img_buffer = BytesIO()
        verify_img.save(img_buffer, format='PNG')
        img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
        
        return jsonify({
            'success': True,
            'verification_image': f'data:image/png;base64,{img_base64}'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/extract_sprites', methods=['POST'])
def extract_sprites():
    """提取精灵预览"""
    if not toolkit or not toolkit.sprite_image:
        return jsonify({'success': False, 'error': '请先加载精灵图'})
    
    try:
        extracted_sprites = []
        
        for sprite in toolkit.sprites:
            # 提取精灵
            sprite_img = toolkit.sprite_image.crop((
                sprite.x, sprite.y, 
                sprite.x + sprite.width, 
                sprite.y + sprite.height
            ))
            
            # 转换为base64
            img_buffer = BytesIO()
            sprite_img.save(img_buffer, format='PNG')
            img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
            
            extracted_sprites.append({
                'id': sprite.id,
                'name': sprite.name,
                'image_data': f'data:image/png;base64,{img_base64}',
                'size': [sprite.width, sprite.height]
            })
        
        return jsonify({
            'success': True,
            'sprites': extracted_sprites
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/backup/list', methods=['GET'])
def list_backups():
    """列出备份文件"""
    if not toolkit:
        return jsonify({'success': False, 'error': '请先初始化项目'})
    
    backups = toolkit.backup_manager.list_backups()
    return jsonify({'success': True, 'backups': backups})

@app.route('/api/backup/restore', methods=['POST'])
def restore_backup():
    """恢复备份"""
    if not toolkit:
        return jsonify({'success': False, 'error': '请先初始化项目'})
    
    data = request.get_json()
    backup_name = data.get('backup_name')
    target_path = data.get('target_path', toolkit.config.runtime_file)
    
    success = toolkit.backup_manager.restore_backup(backup_name, target_path)
    return jsonify({'success': success})

@app.route('/api/export_config', methods=['POST'])
def export_config():
    """导出配置"""
    if not toolkit:
        return jsonify({'success': False, 'error': '请先初始化项目'})
    
    data = request.get_json()
    format_type = data.get('format', 'json')
    
    config_data = {
        'engine': toolkit.config.engine,
        'sprites': [sprite.to_dict() for sprite in toolkit.sprites],
        'metadata': {
            'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
            'sprite_sheet_size': toolkit.sprite_image.size if toolkit.sprite_image else None
        }
    }
    
    if format_type == 'python':
        # 生成Python代码
        python_code = "# Generated sprite coordinates\ncoordinates = [\n"
        for sprite in toolkit.sprites:
            python_code += f"    ({sprite.x}, {sprite.y}, {sprite.width}, {sprite.height}),  # {sprite.name}\n"
        python_code += "]\n"
        
        return jsonify({
            'success': True,
            'config': python_code,
            'format': 'python'
        })
    else:
        return jsonify({
            'success': True,
            'config': config_data,
            'format': 'json'
        })

def run_server(host='localhost', port=5000, project_path='..'):
    """启动服务器"""
    global toolkit
    
    print("🚀 启动精灵图工具包服务器...")
    print(f"📁 项目路径: {os.path.abspath(project_path)}")
    print(f"🌐 服务器地址: http://{host}:{port}")
    print("=" * 50)
    
    # 初始化工具包
    try:
        toolkit = SpriteToolkit(project_path)
        print(f"✅ 检测到游戏引擎: {toolkit.config.engine}")
        print(f"✅ 精灵图路径: {toolkit.config.sprite_sheet_path}")
    except Exception as e:
        print(f"⚠️ 初始化警告: {e}")
    
    app.run(host=host, port=port, debug=True)

if __name__ == '__main__':
    import sys
    
    project_path = sys.argv[1] if len(sys.argv) > 1 else '..'
    run_server(project_path=project_path)
