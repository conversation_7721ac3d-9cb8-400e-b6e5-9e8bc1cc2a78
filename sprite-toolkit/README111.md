# 🎨 Sprite Toolkit - 统一精灵图工具包

一个现代化、功能强大的精灵图编辑和管理工具包，支持多种游戏引擎，提供可视化编辑界面和自动化处理功能。

## ✨ 主要特性

### 🎯 核心功能
- **可视化编辑** - 现代化Web界面，拖拽调整精灵位置
- **智能检测** - 自动识别精灵边界和最优布局
- **实时预览** - 即时查看调整效果和精灵提取结果
- **多引擎支持** - Construct 2/3、Unity、Godot等
- **批量处理** - 同时处理多个精灵图和项目

### 🛠️ 高级特性
- **网格对齐** - 智能网格系统，精确定位
- **备份恢复** - 自动备份，安全回滚
- **多种导出** - Python代码、JSON配置、验证图
- **响应式设计** - 支持不同屏幕尺寸
- **快捷操作** - 键盘快捷键和右键菜单

## 🚀 快速开始

### 一键启动（推荐）
```bash
cd sprite-toolkit
python3 launch.py
```

### 手动启动
```bash
# 方法1: 使用requirements.txt
pip3 install -r requirements.txt
python3 server.py ../

# 方法2: 手动安装依赖
pip3 install Pillow numpy flask flask-cors
python3 server.py ../

# 方法3: 无依赖版本
python3 simple_server.py ../
```

### 🌐 访问地址
- **完整版**: http://localhost:5000
- **简化版**: http://localhost:8080

### ⚠️ 端口说明
如果遇到端口冲突（如macOS的AirPlay占用5000端口），工具会自动切换到其他端口。

## 📁 项目结构

```
sprite-toolkit/
├── 🚀 launch.py              # 一键启动器
├── 🔧 toolkit.py             # 核心工具包类
├── 🌐 server.py              # Web服务器
├── 📱 web/                   # Web界面
│   ├── index.html            # 主界面
│   ├── style.css             # 样式文件
│   └── app.js                # 前端逻辑
├── 🎮 engines/               # 游戏引擎适配器
│   ├── construct2.py         # Construct 2/3支持
│   ├── unity.py              # Unity支持
│   └── godot.py              # Godot支持
├── 🛠️ utils/                 # 工具函数
│   ├── image_analysis.py     # 图像分析
│   ├── coordinate_calc.py    # 坐标计算
│   └── backup_manager.py     # 备份管理
└── 📚 README.md              # 本文档
```

## 🎮 支持的游戏引擎

### ✅ 完全支持
- **Construct 2** - 完整的坐标读取和写入支持
- **Construct 3** - 兼容Construct 2格式

### 🔄 开发中
- **Unity** - 精灵图元数据支持
- **Godot** - .tres资源文件支持
- **通用格式** - JSON/XML配置文件

## 📖 使用指南

### 1. 加载项目
1. 点击 "Load Project" 按钮
2. 输入项目路径（通常是 `../`）
3. 选择精灵图文件路径
4. 点击 "Load" 开始加载

### 2. 调整精灵
#### 自动检测模式
- 点击 "🔍 Auto Detect" 自动识别精灵
- 系统会智能分析精灵边界

#### 手动调整模式
- 点击 "✏️ Manual Mode" 进入手动模式
- 直接在画布上点击设置精灵位置
- 拖拽精灵边框调整大小

#### 网格模式
- 点击 "📐 Grid Mode" 生成规则网格
- 在设置面板调整网格行列数
- 启用 "Snap to Grid" 对齐到网格

### 3. 精确调整
- 在右侧属性面板输入精确数值
- 实时预览精灵提取效果
- 使用缩放工具查看细节

### 4. 保存和导出
- 点击 "Save Changes" 应用到游戏
- 使用导出功能生成配置代码
- 生成验证图检查效果

## 🔧 API 参考

### SpriteToolkit 类

```python
from toolkit import SpriteToolkit

# 初始化
toolkit = SpriteToolkit(project_path="./")

# 加载精灵图
toolkit.load_sprite_sheet("images/sprites.png")

# 分析精灵
sprites = toolkit.analyze_sprites(auto_detect=True)

# 更新坐标
toolkit.update_sprites(sprites)
```

### Web API 端点

```javascript
// 初始化项目
POST /api/init
{
  "project_path": "../"
}

// 加载精灵图
POST /api/load_sprite_sheet
{
  "sprite_path": "images/sprites.png"
}

// 分析精灵
POST /api/analyze_sprites
{
  "auto_detect": true
}

// 更新精灵
POST /api/update_sprites
{
  "sprites": [...]
}
```

## 🎯 最佳实践

### 精灵图设计
- 保持精灵之间有足够间距
- 使用透明背景
- 避免精灵重叠
- 考虑2的幂次尺寸

### 工作流程
1. **备份原文件** - 开始前创建备份
2. **自动检测** - 先尝试自动检测
3. **手动调整** - 根据需要精细调整
4. **验证效果** - 生成验证图检查
5. **测试游戏** - 在游戏中验证效果

### 性能优化
- 使用合适的精灵图尺寸
- 避免过多的小精灵
- 考虑精灵图压缩

## 🔍 故障排除

### 常见问题

#### 精灵显示不正确
**原因**: 坐标计算错误或精灵图格式问题
**解决**: 
1. 检查精灵图是否正确加载
2. 使用自动检测重新分析
3. 手动调整有问题的精灵

#### 无法保存更改
**原因**: 权限问题或文件被锁定
**解决**:
1. 检查文件写入权限
2. 关闭其他编辑器
3. 以管理员身份运行

#### 服务器启动失败
**原因**: 端口被占用或依赖缺失
**解决**:
1. 检查5000端口是否被占用
2. 安装所需依赖包
3. 检查Python版本（需要3.6+）

### 调试模式
```bash
# 启用详细日志
export DEBUG=1
python3 server.py ../

# 检查依赖
python3 -c "import PIL, numpy, flask, flask_cors; print('All dependencies OK')"
```

## 🤝 贡献指南

### 开发环境设置
```bash
# 克隆项目
git clone <repository>
cd sprite-toolkit

# 安装开发依赖
pip3 install -r requirements-dev.txt

# 运行测试
python3 -m pytest tests/
```

### 添加新引擎支持
1. 在 `engines/` 目录创建新的适配器
2. 继承 `BaseEngine` 类
3. 实现必要的方法
4. 添加测试用例

### 代码规范
- 使用 Python 3.6+ 特性
- 遵循 PEP 8 代码风格
- 添加类型注解
- 编写单元测试

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🙏 致谢

- **Pillow** - 图像处理库
- **Flask** - Web框架
- **NumPy** - 数值计算
- **Construct 2** - 游戏引擎参考

## 📞 支持

- 📧 Email: <EMAIL>
- 🐛 Issues: GitHub Issues
- 📖 文档: Wiki页面
- 💬 讨论: Discussions

---

**Sprite Toolkit** - 让精灵图编辑变得简单高效！ 🎨✨
