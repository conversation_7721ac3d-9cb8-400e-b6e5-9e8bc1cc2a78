#!/usr/bin/env python3
"""
🌐 简化版精灵图工具服务器
无需额外依赖，使用Python标准库
"""

import os
import json
import base64
import time
import re
import shutil
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import webbrowser
import threading

class SpriteToolkitHandler(BaseHTTPRequestHandler):
    """HTTP请求处理器"""
    
    def __init__(self, *args, project_path="../", **kwargs):
        self.project_path = os.path.abspath(project_path)
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path

        print(f"GET请求: {path}")  # 调试日志

        if path == '/':
            self.serve_file('web/index.html', 'text/html')
        elif path.startswith('/web/'):
            file_path = path[1:]  # 移除开头的 /
            if path.endswith('.html'):
                self.serve_file(file_path, 'text/html')
            elif path.endswith('.css'):
                self.serve_file(file_path, 'text/css')
            elif path.endswith('.js'):
                self.serve_file(file_path, 'application/javascript')
            else:
                self.send_error(404)
        else:
            self.send_error(404)
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def do_POST(self):
        """处理POST请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path

        # 读取请求数据
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)

        try:
            data = json.loads(post_data.decode('utf-8')) if post_data else {}
        except:
            data = {}

        # API路由
        if path == '/api/init':
            self.handle_init(data)
        elif path == '/api/detect_project':
            self.handle_detect_project(data)
        elif path == '/api/load_sprite_sheet':
            self.handle_load_sprite_sheet(data)
        elif path == '/api/analyze_sprites':
            self.handle_analyze_sprites(data)
        elif path == '/api/update_sprites':
            self.handle_update_sprites(data)
        elif path == '/api/export_config':
            self.handle_export_config(data)
        else:
            self.send_error(404)
    
    def serve_file(self, file_path, content_type):
        """提供静态文件"""
        try:
            full_path = os.path.join(os.path.dirname(__file__), file_path)
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-Type', content_type + '; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
            
        except FileNotFoundError:
            self.send_error(404)
        except Exception as e:
            print(f"Error serving file {file_path}: {e}")
            self.send_error(500)
    
    def send_json_response(self, data):
        """发送JSON响应"""
        response = json.dumps(data, ensure_ascii=False)
        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(response.encode('utf-8'))
    
    def handle_init(self, data):
        """初始化项目"""
        try:
            project_path = data.get('project_path', self.project_path)
            
            # 检测游戏引擎
            engine = "unknown"
            runtime_file = ""
            sprite_sheet_path = ""
            
            if os.path.exists(os.path.join(project_path, "c2runtime.js")):
                engine = "construct2"
                runtime_file = "c2runtime.js"
            
            # 查找精灵图
            images_dir = os.path.join(project_path, "images")
            if os.path.exists(images_dir):
                for file in os.listdir(images_dir):
                    if "fish-sheet" in file and file.endswith(".png"):
                        sprite_sheet_path = os.path.join("images", file)
                        break
            
            self.send_json_response({
                'success': True,
                'config': {
                    'engine': engine,
                    'sprite_sheet_path': sprite_sheet_path,
                    'runtime_file': runtime_file
                }
            })
            
        except Exception as e:
            self.send_json_response({'success': False, 'error': str(e)})

    def handle_detect_project(self, data):
        """检测项目文件结构"""
        try:
            project_path = data.get('project_path', self.project_path)
            full_project_path = os.path.abspath(project_path)

            if not os.path.exists(full_project_path):
                raise FileNotFoundError(f"项目路径不存在: {full_project_path}")

            # 检测文件结构
            detected_files = self.scan_project_structure(full_project_path)

            self.send_json_response({
                'success': True,
                'files': detected_files,
                'sprite_sheets': detected_files.get('sprite_sheets', [])
            })

        except Exception as e:
            self.send_json_response({'success': False, 'error': str(e)})

    def scan_project_structure(self, project_path):
        """扫描项目结构"""
        result = {
            'engine': 'unknown',
            'runtime_files': [],
            'sprite_sheets': [],
            'audio_files': [],
            'config_files': []
        }

        try:
            # 扫描根目录文件
            for item in os.listdir(project_path):
                item_path = os.path.join(project_path, item)

                if os.path.isfile(item_path):
                    # 检测游戏引擎
                    if item == 'c2runtime.js':
                        result['engine'] = 'Construct 2/3'
                        result['runtime_files'].append(item)
                    elif item == 'project.godot':
                        result['engine'] = 'Godot'
                        result['config_files'].append(item)
                    elif item.endswith('.unity'):
                        result['engine'] = 'Unity'
                        result['config_files'].append(item)
                    elif item in ['index.html', 'main.html', 'game.html']:
                        result['runtime_files'].append(item)
                    elif item.endswith('.appcache'):
                        result['config_files'].append(item)
                    elif item.endswith('.manifest'):
                        result['config_files'].append(item)

                elif os.path.isdir(item_path):
                    # 扫描子目录
                    if item in ['images', 'sprites', 'textures', 'assets']:
                        self.scan_image_directory(item_path, result['sprite_sheets'], item)
                    elif item in ['audio', 'sounds', 'media']:
                        self.scan_audio_directory(item_path, result['audio_files'], item)
                    elif item == 'Assets' and result['engine'] == 'unknown':
                        result['engine'] = 'Unity'

            # 如果没有检测到引擎，尝试通过文件特征判断
            if result['engine'] == 'unknown':
                if any('.js' in f for f in result['runtime_files']):
                    result['engine'] = 'Web Game'
                elif result['sprite_sheets']:
                    result['engine'] = 'Generic Game'

            return result

        except Exception as e:
            print(f"扫描项目结构失败: {e}")
            return result

    def scan_image_directory(self, dir_path, sprite_list, dir_name):
        """扫描图像目录"""
        try:
            for item in os.listdir(dir_path):
                if item.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                    relative_path = os.path.join(dir_name, item)
                    sprite_list.append(relative_path)

                    # 限制返回数量，避免列表过长
                    if len(sprite_list) >= 20:
                        break
        except Exception as e:
            print(f"扫描图像目录失败: {e}")

    def scan_audio_directory(self, dir_path, audio_list, dir_name):
        """扫描音频目录"""
        try:
            for item in os.listdir(dir_path):
                if item.lower().endswith(('.mp3', '.wav', '.ogg', '.m4a', '.aac')):
                    relative_path = os.path.join(dir_name, item)
                    audio_list.append(relative_path)

                    # 限制返回数量
                    if len(audio_list) >= 10:
                        break
        except Exception as e:
            print(f"扫描音频目录失败: {e}")

    def handle_load_sprite_sheet(self, data):
        """加载精灵图"""
        try:
            sprite_path = data.get('sprite_path', '')
            full_path = os.path.join(self.project_path, sprite_path)
            
            if not os.path.exists(full_path):
                raise FileNotFoundError(f"精灵图不存在: {full_path}")
            
            # 读取图片并转换为base64
            with open(full_path, 'rb') as f:
                img_data = f.read()
            
            img_base64 = base64.b64encode(img_data).decode()
            
            # 简单获取图片尺寸（需要PIL库才能准确获取，这里使用估算）
            size = [1024, 1024]  # 默认尺寸
            
            self.send_json_response({
                'success': True,
                'image_data': f'data:image/png;base64,{img_base64}',
                'size': size
            })
            
        except Exception as e:
            self.send_json_response({'success': False, 'error': str(e)})
    
    def handle_analyze_sprites(self, data):
        """分析精灵图"""
        try:
            auto_detect = data.get('auto_detect', True)
            
            if auto_detect:
                # 生成3x3网格布局
                sprites = []
                for i in range(9):
                    row = i // 3
                    col = i % 3
                    sprites.append({
                        'id': i,
                        'x': col * 258,
                        'y': row * 311,
                        'width': 258,
                        'height': 311,
                        'name': f'Sprite {i + 1}'
                    })
            else:
                # 从运行时文件加载当前坐标
                sprites = self.load_current_sprites()
            
            self.send_json_response({
                'success': True,
                'sprites': sprites
            })
            
        except Exception as e:
            self.send_json_response({'success': False, 'error': str(e)})
    
    def load_current_sprites(self, sprite_filename=None):
        """从运行时文件加载当前精灵坐标"""
        try:
            runtime_path = os.path.join(self.project_path, "c2runtime.js")
            with open(runtime_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 如果没有指定文件名，尝试自动检测
            if not sprite_filename:
                sprite_filename = self.detect_sprite_filename(content)

            if not sprite_filename:
                print("未找到精灵图文件引用")
                return []

            # 生成动态匹配模式
            escaped_filename = re.escape(sprite_filename)
            pattern = rf'\["{escaped_filename}",\s*\d+,\s*(\d+),\s*(\d+),\s*(\d+),\s*(\d+)'
            matches = re.findall(pattern, content)

            sprites = []
            for i, (x, y, w, h) in enumerate(matches):
                sprites.append({
                    'id': i,
                    'x': int(x),
                    'y': int(y),
                    'width': int(w),
                    'height': int(h),
                    'name': f'Sprite {i + 1}'
                })

            print(f"从 {sprite_filename} 加载了 {len(sprites)} 个精灵坐标")
            return sprites

        except Exception as e:
            print(f"加载精灵坐标失败: {e}")
            return []

    def detect_sprite_filename(self, content):
        """自动检测精灵图文件名"""
        # 查找所有图片文件引用
        pattern = r'\["([^"]+\.(?:png|jpg|jpeg|gif|bmp))"'
        matches = re.findall(pattern, content, re.IGNORECASE)

        # 优先选择包含sprite、sheet等关键词的文件
        priority_keywords = ['sprite', 'sheet', 'atlas']
        for match in matches:
            for keyword in priority_keywords:
                if keyword in match.lower():
                    return match

        # 如果没有找到优先文件，返回第一个图片文件
        return matches[0] if matches else None
    
    def handle_update_sprites(self, data):
        """更新精灵坐标"""
        try:
            sprites = data.get('sprites', [])
            sprite_filename = data.get('sprite_filename')

            # 验证输入数据
            if not sprites:
                raise ValueError("没有提供精灵数据")

            for i, sprite in enumerate(sprites):
                self.validate_sprite_data(sprite, i)

            # 备份原文件
            self.create_backup("c2runtime.js")

            runtime_path = os.path.join(self.project_path, "c2runtime.js")
            with open(runtime_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 自动检测精灵图文件名（如果未提供）
            if not sprite_filename:
                sprite_filename = self.detect_sprite_filename(content)

            if not sprite_filename:
                raise ValueError("无法检测到精灵图文件名")

            # 生成动态匹配模式
            escaped_filename = re.escape(sprite_filename)
            pattern = rf'(\["{escaped_filename}",\s*\d+,\s*)(\d+),\s*(\d+),\s*(\d+),\s*(\d+)(\s*,\s*[^\]]+\])'
            matches = list(re.finditer(pattern, content))

            if len(matches) != len(sprites):
                print(f"警告: 找到 {len(matches)} 个匹配项，但有 {len(sprites)} 个精灵数据")

            updated_content = content
            for i, match in enumerate(matches):
                if i < len(sprites):
                    sprite = sprites[i]
                    new_coords = f"{match.group(1)}{sprite['x']}, {sprite['y']}, {sprite['width']}, {sprite['height']}{match.group(6)}"
                    updated_content = updated_content.replace(match.group(0), new_coords)

            # 保存更新后的文件
            with open(runtime_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)

            # 更新缓存版本
            self.update_cache_version()

            print(f"✅ 成功更新 {len(sprites)} 个精灵坐标到 {sprite_filename}")
            self.send_json_response({'success': True, 'updated_count': len(sprites)})

        except Exception as e:
            print(f"❌ 更新精灵坐标失败: {e}")
            self.send_json_response({'success': False, 'error': str(e)})

    def validate_sprite_data(self, sprite, index):
        """验证精灵数据格式"""
        required_fields = ['id', 'x', 'y', 'width', 'height']

        for field in required_fields:
            if field not in sprite:
                raise ValueError(f"精灵 {index}: 缺少必需字段 '{field}'")

            if not isinstance(sprite[field], (int, float)):
                raise ValueError(f"精灵 {index}: 字段 '{field}' 必须是数字")

            if field in ['width', 'height'] and sprite[field] <= 0:
                raise ValueError(f"精灵 {index}: '{field}' 必须大于0")

            if field in ['x', 'y'] and sprite[field] < 0:
                raise ValueError(f"精灵 {index}: '{field}' 不能为负数")
    
    def handle_export_config(self, data):
        """导出配置"""
        try:
            format_type = data.get('format', 'json')
            sprites = data.get('sprites', [])

            if not sprites:
                raise ValueError("没有精灵数据可导出")

            if format_type == 'python':
                # 生成Python代码
                python_code = "# Generated sprite coordinates\n"
                python_code += f"# Total sprites: {len(sprites)}\n"
                python_code += f"# Generated at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                python_code += "coordinates = [\n"
                for sprite in sprites:
                    python_code += f"    ({sprite['x']}, {sprite['y']}, {sprite['width']}, {sprite['height']}),  # {sprite['name']}\n"
                python_code += "]\n\n"
                python_code += "# Usage example:\n"
                python_code += "# for i, (x, y, w, h) in enumerate(coordinates):\n"
                python_code += "#     print(f'Sprite {i}: x={x}, y={y}, width={w}, height={h}')\n"

                self.send_json_response({
                    'success': True,
                    'config': python_code,
                    'format': 'python'
                })
            elif format_type == 'css':
                # 生成CSS精灵图样式
                css_code = "/* Generated CSS sprite coordinates */\n"
                css_code += f"/* Total sprites: {len(sprites)} */\n"
                css_code += f"/* Generated at: {time.strftime('%Y-%m-%d %H:%M:%S')} */\n\n"
                css_code += ".sprite {\n"
                css_code += "    background-image: url('sprite-sheet.png');\n"
                css_code += "    background-repeat: no-repeat;\n"
                css_code += "    display: inline-block;\n"
                css_code += "}\n\n"

                for sprite in sprites:
                    class_name = sprite['name'].lower().replace(' ', '-').replace('_', '-')
                    css_code += f".sprite-{class_name} {{\n"
                    css_code += f"    background-position: -{sprite['x']}px -{sprite['y']}px;\n"
                    css_code += f"    width: {sprite['width']}px;\n"
                    css_code += f"    height: {sprite['height']}px;\n"
                    css_code += "}\n\n"

                self.send_json_response({
                    'success': True,
                    'config': css_code,
                    'format': 'css'
                })
            elif format_type == 'javascript':
                # 生成JavaScript对象
                js_code = "// Generated JavaScript sprite coordinates\n"
                js_code += f"// Total sprites: {len(sprites)}\n"
                js_code += f"// Generated at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                js_code += "const spriteCoordinates = {\n"
                for i, sprite in enumerate(sprites):
                    sprite_name = sprite['name'].replace(' ', '_').replace('-', '_')
                    js_code += f"    {sprite_name}: {{\n"
                    js_code += f"        x: {sprite['x']},\n"
                    js_code += f"        y: {sprite['y']},\n"
                    js_code += f"        width: {sprite['width']},\n"
                    js_code += f"        height: {sprite['height']}\n"
                    js_code += "    }" + ("," if i < len(sprites) - 1 else "") + "\n"
                js_code += "};\n\n"
                js_code += "// Usage example:\n"
                js_code += "// const sprite = spriteCoordinates.Sprite_1;\n"
                js_code += "// drawSprite(sprite.x, sprite.y, sprite.width, sprite.height);\n"

                self.send_json_response({
                    'success': True,
                    'config': js_code,
                    'format': 'javascript'
                })
            else:
                # JSON格式
                config_data = {
                    'engine': 'construct2',
                    'sprites': sprites,
                    'metadata': {
                        'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                        'tool': 'Sprite Toolkit Simple',
                        'total_sprites': len(sprites)
                    }
                }

                self.send_json_response({
                    'success': True,
                    'config': config_data,
                    'format': 'json'
                })

        except Exception as e:
            self.send_json_response({'success': False, 'error': str(e)})
    
    def create_backup(self, filename):
        """创建备份文件"""
        try:
            backup_dir = os.path.join(self.project_path, "backup_sprites")
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
            
            source_path = os.path.join(self.project_path, filename)
            if os.path.exists(source_path):
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                name, ext = os.path.splitext(filename)
                backup_name = f"{name}_backup_{timestamp}{ext}"
                backup_path = os.path.join(backup_dir, backup_name)
                
                shutil.copy2(source_path, backup_path)
                print(f"📦 已备份: {backup_name}")
                
        except Exception as e:
            print(f"备份失败: {e}")
    
    def update_cache_version(self):
        """更新缓存版本"""
        try:
            cache_path = os.path.join(self.project_path, "offline.appcache")
            if not os.path.exists(cache_path):
                return
            
            with open(cache_path, 'r') as f:
                content = f.read()
            
            # 更新版本号
            version_pattern = r'# Version: ([\d\.]+)'
            match = re.search(version_pattern, content)
            
            if match:
                old_version = match.group(1)
                version_parts = old_version.split('.')
                version_parts[-1] = str(int(version_parts[-1]) + 1)
                new_version = '.'.join(version_parts)
            else:
                new_version = "1.0.1"
            
            updated_content = re.sub(
                r'# Version: [\d\.]+\n# Last Updated: [^\n]+',
                f'# Version: {new_version}\n# Last Updated: {time.strftime("%Y-%m-%d")} - Simple toolkit update',
                content
            )
            
            with open(cache_path, 'w') as f:
                f.write(updated_content)
            
            print(f"✅ 缓存版本已更新到 {new_version}")
            
        except Exception as e:
            print(f"更新缓存版本失败: {e}")

def create_handler_class(project_path):
    """创建带项目路径的处理器类"""
    class Handler(SpriteToolkitHandler):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, project_path=project_path, **kwargs)
    return Handler

def run_simple_server(project_path="../", host='localhost', port=8080):
    """运行简化版服务器"""
    print("🚀 启动 Sprite Toolkit 简化版服务器...")
    print(f"📁 项目路径: {os.path.abspath(project_path)}")
    print(f"🌐 服务器地址: http://{host}:{port}")
    print("💡 此版本无需额外依赖，使用Python标准库")
    print("=" * 50)
    
    handler_class = create_handler_class(project_path)
    server = HTTPServer((host, port), handler_class)
    
    # 在新线程中打开浏览器
    def open_browser():
        time.sleep(2)
        webbrowser.open(f'http://{host}:{port}')
    
    threading.Thread(target=open_browser, daemon=True).start()
    
    try:
        print("✅ 服务器已启动，按 Ctrl+C 停止")
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务器...")
        server.shutdown()
        print("✅ 服务器已停止")

if __name__ == '__main__':
    import sys
    project_path = sys.argv[1] if len(sys.argv) > 1 else '../'
    run_simple_server(project_path)
