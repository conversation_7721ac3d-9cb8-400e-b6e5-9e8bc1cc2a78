# 🎮 Sprite Toolkit 用户手册

## 📖 目录
- [快速开始](#快速开始)
- [功能介绍](#功能介绍)
- [使用教程](#使用教程)
- [支持的引擎](#支持的引擎)
- [导出格式](#导出格式)
- [故障排除](#故障排除)
- [高级功能](#高级功能)

## 🚀 快速开始

### 系统要求
- Python 3.6+
- 现代浏览器 (Chrome, Firefox, Safari, Edge)
- 2GB+ 可用内存

### 安装和启动

#### 方法1: 一键启动 (推荐)
```bash
cd sprite-toolkit
python3 launch.py
```

#### 方法2: 手动启动
```bash
# 安装依赖
pip3 install -r requirements.txt

# 启动服务器
python3 server.py ../

# 或使用简化版本
python3 simple_server.py ../
```

### 首次使用
1. 启动工具后，浏览器会自动打开
2. 点击右上角的"Load Project"按钮
3. 选择你的游戏项目文件夹
4. 选择精灵图文件
5. 开始编辑！

## 🛠️ 功能介绍

### 核心功能
- **🔍 项目检测** - 自动识别游戏引擎类型
- **📊 精灵编辑** - 可视化调整精灵坐标
- **🌍 多语言** - 支持中英文界面切换
- **📤 多格式导出** - Python/CSS/JavaScript/JSON
- **💾 自动备份** - 保护原始文件安全

### 界面布局
```
┌─────────────────────────────────────────┐
│ 🎨 Sprite Toolkit    [EN] [中文] [Load] │
├─────────────┬───────────────────────────┤
│ 🔧 工具栏   │                           │
│ 📊 精灵列表 │      🖼️ 画布区域          │
│ ⚙️ 设置面板 │                           │
├─────────────┼───────────────────────────┤
│ 📝 属性面板 │      🎯 缩放工具          │
│ 🖼️ 预览区域 │                           │
│ 📤 导出选项 │                           │
└─────────────┴───────────────────────────┘
```

## 📚 使用教程

### 1. 加载项目
1. **点击"Load Project"**
2. **输入项目路径** - 例如: `../my-game/`
3. **选择精灵图** - 例如: `images/sprite-sheet.png`
4. **选择引擎** (可选) - 通常自动检测即可
5. **点击"Load"**

### 2. 编辑精灵坐标

#### 自动检测模式
- 点击"🔍 Auto Detect"
- 系统自动分析精灵图
- 生成初始坐标网格

#### 手动编辑模式
- 点击"✏️ Manual Mode"
- 在画布上拖拽调整精灵边界
- 使用属性面板精确输入数值

#### 网格模式
- 点击"📐 Grid Mode"
- 设置网格行列数
- 自动对齐到网格

### 3. 导出结果

#### Python代码
```python
# Generated sprite coordinates
coordinates = [
    (0, 0, 100, 100),    # Sprite 1
    (100, 0, 100, 100),  # Sprite 2
    (200, 0, 100, 100),  # Sprite 3
]
```

#### CSS精灵图
```css
.sprite {
    background-image: url('sprite-sheet.png');
    background-repeat: no-repeat;
}

.sprite-player {
    background-position: -0px -0px;
    width: 100px;
    height: 100px;
}
```

#### JavaScript对象
```javascript
const spriteCoordinates = {
    Player: { x: 0, y: 0, width: 100, height: 100 },
    Enemy: { x: 100, y: 0, width: 100, height: 100 }
};
```

## 🎮 支持的引擎

### ✅ 完全支持
- **Construct 2/3** - 完整的坐标读取和写入
- **Web游戏** - 通用HTML5游戏支持

### 🔄 基础支持
- **Godot** - 项目检测和精灵编辑
- **Unity** - 项目检测和精灵编辑
- **通用项目** - 适用于任何精灵图项目

### 🔧 扩展支持
工具设计为可扩展架构，可以轻松添加新引擎支持。

## 📤 导出格式

| 格式 | 用途 | 适用场景 |
|------|------|----------|
| 🐍 Python | 游戏开发 | Pygame, Arcade等 |
| 📄 JSON | 配置文件 | 通用数据交换 |
| 🎨 CSS | Web开发 | 网页精灵图 |
| 📜 JavaScript | 前端开发 | HTML5游戏 |

## 🔧 故障排除

### 常见问题

#### 1. 端口被占用
```
错误: Address already in use
解决: 工具会自动尝试其他端口 (8081, 8082等)
```

#### 2. 精灵图加载失败
```
错误: Failed to load sprite sheet
解决: 
- 检查文件路径是否正确
- 确保图片格式支持 (PNG, JPG, GIF)
- 检查文件权限
```

#### 3. 坐标保存失败
```
错误: Failed to save coordinates
解决:
- 检查项目文件是否可写
- 确保有足够的磁盘空间
- 检查备份文件夹权限
```

### 调试模式
```bash
# 启用详细日志
python3 simple_server.py ../ --debug

# 查看日志文件
tail -f sprite-toolkit.log
```

## 🔬 高级功能

### 配置文件
编辑 `config.json` 自定义工具行为:
```json
{
  "ui": {
    "default_language": "zh",
    "auto_save": true,
    "grid_size": 50
  },
  "server": {
    "default_port": 8080,
    "auto_open_browser": true
  }
}
```

### 批量处理
```bash
# 处理多个精灵图 (计划功能)
python3 batch_process.py --input-dir sprites/ --output-dir output/
```

### 插件开发
```python
# 自定义导出格式 (计划功能)
class CustomExporter:
    def export(self, sprites):
        # 自定义导出逻辑
        pass
```

## 📞 支持和反馈

### 获取帮助
- 📖 查看本手册
- 🐛 报告问题: GitHub Issues
- 💡 功能建议: GitHub Discussions
- 📧 联系开发者

### 贡献代码
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

---

**🎉 享受使用 Sprite Toolkit！**

*版本: 1.0.0 | 更新时间: 2025-06-21*
