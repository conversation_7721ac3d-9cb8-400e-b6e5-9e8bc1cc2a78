#!/usr/bin/env python3
"""
🧪 Sprite Toolkit 测试套件
验证工具的核心功能和逻辑
"""

import os
import sys
import tempfile
import shutil
import json
import re

class TestSpriteToolkit:
    """精灵图工具包测试类"""

    def __init__(self):
        self.test_dir = None

    def setup_test_environment(self):
        """设置测试环境"""
        # 创建临时测试目录
        self.test_dir = tempfile.mkdtemp(prefix="sprite_test_")
        print(f"📁 创建测试目录: {self.test_dir}")

        # 创建模拟项目结构
        self.create_mock_project()
        
    def create_mock_project(self):
        """创建模拟项目结构"""
        # 创建images目录
        images_dir = os.path.join(self.test_dir, "images")
        os.makedirs(images_dir, exist_ok=True)
        
        # 创建模拟精灵图文件（空文件）
        sprite_file = os.path.join(images_dir, "test-sprite-sheet.png")
        with open(sprite_file, 'wb') as f:
            f.write(b'\x89PNG\r\n\x1a\n')  # PNG文件头
            
        # 创建模拟运行时文件
        runtime_content = '''
// Mock runtime file
var sprites = [
    ["images/test-sprite-sheet.png", 0, 0, 0, 100, 100, 1, 0.5, 0.5, [], [], 0],
    ["images/test-sprite-sheet.png", 0, 100, 0, 100, 100, 1, 0.5, 0.5, [], [], 0],
    ["images/test-sprite-sheet.png", 0, 200, 0, 100, 100, 1, 0.5, 0.5, [], [], 0]
];
'''
        runtime_file = os.path.join(self.test_dir, "c2runtime.js")
        with open(runtime_file, 'w', encoding='utf-8') as f:
            f.write(runtime_content)
            
        print("✅ 模拟项目结构创建完成")
        
    def scan_project_structure(self, project_path):
        """独立的项目结构扫描方法"""
        result = {
            'engine': 'unknown',
            'runtime_files': [],
            'sprite_sheets': [],
            'audio_files': [],
            'config_files': []
        }

        try:
            for item in os.listdir(project_path):
                item_path = os.path.join(project_path, item)

                if os.path.isfile(item_path):
                    if item == 'c2runtime.js':
                        result['engine'] = 'Construct 2/3'
                        result['runtime_files'].append(item)
                    elif item == 'project.godot':
                        result['engine'] = 'Godot'
                        result['config_files'].append(item)
                    elif item in ['index.html', 'main.html', 'game.html']:
                        result['runtime_files'].append(item)

                elif os.path.isdir(item_path):
                    if item in ['images', 'sprites', 'textures', 'assets']:
                        self.scan_image_directory(item_path, result['sprite_sheets'], item)

            if result['engine'] == 'unknown' and result['sprite_sheets']:
                result['engine'] = 'Generic Game'

            return result

        except Exception as e:
            print(f"扫描项目结构失败: {e}")
            return result

    def scan_image_directory(self, dir_path, sprite_list, dir_name):
        """扫描图像目录"""
        try:
            for item in os.listdir(dir_path):
                if item.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                    relative_path = os.path.join(dir_name, item)
                    sprite_list.append(relative_path)
        except Exception as e:
            print(f"扫描图像目录失败: {e}")

    def test_project_detection(self):
        """测试项目检测功能"""
        print("\n🔍 测试项目检测...")

        try:
            result = self.scan_project_structure(self.test_dir)

            # 验证检测结果
            assert result['engine'] == 'Construct 2/3', f"引擎检测错误: {result['engine']}"
            assert len(result['sprite_sheets']) > 0, "未检测到精灵图"
            assert 'c2runtime.js' in result['runtime_files'], "未检测到运行时文件"

            print("✅ 项目检测测试通过")
            return True

        except Exception as e:
            print(f"❌ 项目检测测试失败: {e}")
            return False
            
    def load_current_sprites(self, sprite_filename=None):
        """独立的精灵加载方法"""
        try:
            runtime_path = os.path.join(self.test_dir, "c2runtime.js")
            with open(runtime_path, 'r', encoding='utf-8') as f:
                content = f.read()

            if not sprite_filename:
                sprite_filename = self.detect_sprite_filename(content)

            if not sprite_filename:
                return []

            escaped_filename = re.escape(sprite_filename)
            pattern = rf'\["{escaped_filename}",\s*\d+,\s*(\d+),\s*(\d+),\s*(\d+),\s*(\d+)'
            matches = re.findall(pattern, content)

            sprites = []
            for i, (x, y, w, h) in enumerate(matches):
                sprites.append({
                    'id': i,
                    'x': int(x),
                    'y': int(y),
                    'width': int(w),
                    'height': int(h),
                    'name': f'Sprite {i + 1}'
                })

            return sprites

        except Exception as e:
            print(f"加载精灵坐标失败: {e}")
            return []

    def detect_sprite_filename(self, content):
        """检测精灵图文件名"""
        pattern = r'\["([^"]+\.(?:png|jpg|jpeg|gif|bmp))"'
        matches = re.findall(pattern, content, re.IGNORECASE)

        priority_keywords = ['sprite', 'sheet', 'atlas']
        for match in matches:
            for keyword in priority_keywords:
                if keyword in match.lower():
                    return match

        return matches[0] if matches else None

    def test_sprite_loading(self):
        """测试精灵加载功能"""
        print("\n📊 测试精灵加载...")

        try:
            sprites = self.load_current_sprites()

            # 验证加载结果
            assert len(sprites) == 3, f"精灵数量错误: {len(sprites)}"

            for i, sprite in enumerate(sprites):
                assert 'id' in sprite, f"精灵 {i} 缺少id字段"
                assert 'x' in sprite, f"精灵 {i} 缺少x字段"
                assert 'y' in sprite, f"精灵 {i} 缺少y字段"
                assert 'width' in sprite, f"精灵 {i} 缺少width字段"
                assert 'height' in sprite, f"精灵 {i} 缺少height字段"

            print("✅ 精灵加载测试通过")
            return True

        except Exception as e:
            print(f"❌ 精灵加载测试失败: {e}")
            return False
            
    def validate_sprite_data(self, sprite, index):
        """独立的精灵数据验证方法"""
        required_fields = ['id', 'x', 'y', 'width', 'height']

        for field in required_fields:
            if field not in sprite:
                raise ValueError(f"精灵 {index}: 缺少必需字段 '{field}'")

            if not isinstance(sprite[field], (int, float)):
                raise ValueError(f"精灵 {index}: 字段 '{field}' 必须是数字")

            if field in ['width', 'height'] and sprite[field] <= 0:
                raise ValueError(f"精灵 {index}: '{field}' 必须大于0")

            if field in ['x', 'y'] and sprite[field] < 0:
                raise ValueError(f"精灵 {index}: '{field}' 不能为负数")

    def test_sprite_validation(self):
        """测试精灵数据验证"""
        print("\n🔧 测试数据验证...")

        try:
            # 测试有效数据
            valid_sprite = {'id': 0, 'x': 10, 'y': 20, 'width': 100, 'height': 150}
            self.validate_sprite_data(valid_sprite, 0)

            # 测试无效数据
            invalid_sprites = [
                {'x': 10, 'y': 20, 'width': 100, 'height': 150},  # 缺少id
                {'id': 0, 'x': 10, 'y': 20, 'width': -100, 'height': 150},  # 负宽度
                {'id': 0, 'x': 10, 'y': 20, 'width': 100, 'height': 0},  # 零高度
                {'id': 0, 'x': -10, 'y': 20, 'width': 100, 'height': 150},  # 负坐标
            ]

            for i, invalid_sprite in enumerate(invalid_sprites):
                try:
                    self.validate_sprite_data(invalid_sprite, i)
                    assert False, f"应该拒绝无效数据 {i}"
                except ValueError:
                    pass  # 预期的错误

            print("✅ 数据验证测试通过")
            return True

        except Exception as e:
            print(f"❌ 数据验证测试失败: {e}")
            return False
            
    def test_filename_detection(self):
        """测试文件名检测"""
        print("\n🎯 测试文件名检测...")

        try:
            # 创建包含多个图片引用的内容
            test_content = '''
            ["images/background.png", 0, 0, 0, 800, 600],
            ["images/test-sprite-sheet.png", 0, 0, 0, 100, 100],
            ["images/ui-elements.png", 0, 0, 0, 50, 50]
            '''

            detected = self.detect_sprite_filename(test_content)

            # 应该优先选择包含sprite关键词的文件
            assert detected == "images/test-sprite-sheet.png", f"文件名检测错误: {detected}"

            print("✅ 文件名检测测试通过")
            return True

        except Exception as e:
            print(f"❌ 文件名检测测试失败: {e}")
            return False
            
    def cleanup(self):
        """清理测试环境"""
        if self.test_dir and os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
            print(f"🧹 清理测试目录: {self.test_dir}")
            
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始运行 Sprite Toolkit 测试套件")
        print("=" * 50)
        
        try:
            self.setup_test_environment()
            
            tests = [
                self.test_project_detection,
                self.test_sprite_loading,
                self.test_sprite_validation,
                self.test_filename_detection
            ]
            
            passed = 0
            total = len(tests)
            
            for test in tests:
                if test():
                    passed += 1
                    
            print("\n" + "=" * 50)
            print(f"📊 测试结果: {passed}/{total} 通过")
            
            if passed == total:
                print("🎉 所有测试通过！工具逻辑正确。")
                return True
            else:
                print("⚠️  部分测试失败，需要修复。")
                return False
                
        except Exception as e:
            print(f"💥 测试运行失败: {e}")
            return False
            
        finally:
            self.cleanup()

if __name__ == '__main__':
    tester = TestSpriteToolkit()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
