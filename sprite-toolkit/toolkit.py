#!/usr/bin/env python3
"""
🎨 统一精灵图工具包
整合所有精灵图处理功能的核心类
"""

import os
import re
import json
import shutil
import time
from typing import List, Tuple, Dict, Optional, Any
from dataclasses import dataclass
from PIL import Image, ImageDraw, ImageFont
import numpy as np

@dataclass
class SpriteInfo:
    """精灵信息数据类"""
    id: int
    x: int
    y: int
    width: int
    height: int
    name: str = ""
    
    def to_dict(self) -> Dict:
        return {
            'id': self.id,
            'x': self.x,
            'y': self.y,
            'width': self.width,
            'height': self.height,
            'name': self.name
        }

@dataclass
class ProjectConfig:
    """项目配置数据类"""
    engine: str = "construct2"
    sprite_sheet_path: str = ""
    runtime_file: str = ""
    backup_dir: str = "backup_sprites"
    cache_file: str = "offline.appcache"

class SpriteToolkit:
    """统一的精灵图工具包"""
    
    def __init__(self, project_path: str = "."):
        self.project_path = os.path.abspath(project_path)
        self.config = ProjectConfig()
        self.sprites: List[SpriteInfo] = []
        self.sprite_image: Optional[Image.Image] = None
        self.backup_manager = BackupManager(self.project_path)
        
        # 自动检测项目配置
        self._detect_project_config()
    
    def _detect_project_config(self):
        """自动检测项目配置"""
        # 检测游戏引擎
        if os.path.exists(os.path.join(self.project_path, "c2runtime.js")):
            self.config.engine = "construct2"
            self.config.runtime_file = "c2runtime.js"
        elif os.path.exists(os.path.join(self.project_path, "project.godot")):
            self.config.engine = "godot"
        elif os.path.exists(os.path.join(self.project_path, "Assets")):
            self.config.engine = "unity"
        
        # 检测精灵图
        images_dir = os.path.join(self.project_path, "images")
        if os.path.exists(images_dir):
            for file in os.listdir(images_dir):
                if "fish-sheet" in file and file.endswith(".png"):
                    self.config.sprite_sheet_path = os.path.join("images", file)
                    break
    
    def load_sprite_sheet(self, path: str = None) -> bool:
        """加载精灵图"""
        try:
            sprite_path = path or self.config.sprite_sheet_path
            full_path = os.path.join(self.project_path, sprite_path)
            
            if not os.path.exists(full_path):
                raise FileNotFoundError(f"精灵图不存在: {full_path}")
            
            self.sprite_image = Image.open(full_path)
            print(f"✅ 已加载精灵图: {sprite_path} ({self.sprite_image.size})")
            return True
            
        except Exception as e:
            print(f"❌ 加载精灵图失败: {e}")
            return False
    
    def analyze_sprites(self, auto_detect: bool = True) -> List[SpriteInfo]:
        """分析精灵图，检测精灵位置"""
        if not self.sprite_image:
            print("❌ 请先加载精灵图")
            return []
        
        if auto_detect:
            return self._auto_detect_sprites()
        else:
            return self._load_current_sprites()
    
    def _auto_detect_sprites(self) -> List[SpriteInfo]:
        """自动检测精灵边界"""
        try:
            # 转换为numpy数组进行分析
            img_array = np.array(self.sprite_image)
            
            # 检测非透明区域
            if img_array.shape[2] == 4:  # RGBA
                alpha = img_array[:, :, 3]
                non_transparent = alpha > 0
            else:  # RGB
                # 假设白色为透明
                non_transparent = ~np.all(img_array == [255, 255, 255], axis=2)
            
            # 使用连通组件分析检测独立的精灵
            sprites = self._find_connected_components(non_transparent)
            
            print(f"🔍 自动检测到 {len(sprites)} 个精灵")
            return sprites
            
        except Exception as e:
            print(f"❌ 自动检测失败: {e}")
            return self._generate_grid_layout()
    
    def _find_connected_components(self, mask: np.ndarray) -> List[SpriteInfo]:
        """查找连通组件"""
        # 简化版连通组件分析
        sprites = []
        
        # 扫描网格，寻找精灵区域
        rows, cols = mask.shape
        visited = np.zeros_like(mask, dtype=bool)
        
        sprite_id = 0
        for i in range(0, rows, 50):  # 粗略扫描
            for j in range(0, cols, 50):
                if mask[i, j] and not visited[i, j]:
                    # 找到新的精灵区域
                    sprite_bounds = self._trace_sprite_bounds(mask, i, j, visited)
                    if sprite_bounds:
                        x, y, w, h = sprite_bounds
                        if w > 50 and h > 50:  # 过滤太小的区域
                            sprites.append(SpriteInfo(
                                id=sprite_id,
                                x=x, y=y, width=w, height=h,
                                name=f"Sprite {sprite_id + 1}"
                            ))
                            sprite_id += 1
        
        return sprites[:9]  # 限制为9个精灵
    
    def _trace_sprite_bounds(self, mask: np.ndarray, start_i: int, start_j: int, 
                           visited: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """追踪精灵边界"""
        rows, cols = mask.shape
        min_i, max_i = start_i, start_i
        min_j, max_j = start_j, start_j
        
        # 简单的边界框查找
        for i in range(max(0, start_i - 200), min(rows, start_i + 200)):
            for j in range(max(0, start_j - 200), min(cols, start_j + 200)):
                if mask[i, j]:
                    min_i, max_i = min(min_i, i), max(max_i, i)
                    min_j, max_j = min(min_j, j), max(max_j, j)
                    visited[i, j] = True
        
        if max_i > min_i and max_j > min_j:
            return (min_j, min_i, max_j - min_j, max_i - min_i)
        return None
    
    def _generate_grid_layout(self) -> List[SpriteInfo]:
        """生成3x3网格布局"""
        if not self.sprite_image:
            return []
        
        width, height = self.sprite_image.size
        sprite_width = width // 3
        sprite_height = height // 3
        
        sprites = []
        for i in range(9):
            row = i // 3
            col = i % 3
            sprites.append(SpriteInfo(
                id=i,
                x=col * sprite_width,
                y=row * sprite_height,
                width=sprite_width,
                height=sprite_height,
                name=f"Sprite {i + 1}"
            ))
        
        return sprites
    
    def _load_current_sprites(self) -> List[SpriteInfo]:
        """从游戏文件加载当前精灵坐标"""
        if self.config.engine == "construct2":
            return self._load_construct2_sprites()
        else:
            return self._generate_grid_layout()
    
    def _load_construct2_sprites(self) -> List[SpriteInfo]:
        """从Construct2运行时文件加载精灵坐标"""
        try:
            runtime_path = os.path.join(self.project_path, self.config.runtime_file)
            with open(runtime_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找精灵坐标
            pattern = r'\["images/fish-sheet0\.png",\s*\d+,\s*(\d+),\s*(\d+),\s*(\d+),\s*(\d+)'
            matches = re.findall(pattern, content)
            
            sprites = []
            for i, (x, y, w, h) in enumerate(matches):
                sprites.append(SpriteInfo(
                    id=i,
                    x=int(x), y=int(y), width=int(w), height=int(h),
                    name=f"Sprite {i + 1}"
                ))
            
            print(f"📋 从运行时文件加载了 {len(sprites)} 个精灵坐标")
            return sprites
            
        except Exception as e:
            print(f"❌ 加载精灵坐标失败: {e}")
            return self._generate_grid_layout()
    
    def update_sprites(self, sprites: List[SpriteInfo]) -> bool:
        """更新精灵坐标"""
        self.sprites = sprites
        
        if self.config.engine == "construct2":
            return self._update_construct2_sprites()
        else:
            print(f"⚠️ 暂不支持 {self.config.engine} 引擎")
            return False
    
    def _update_construct2_sprites(self) -> bool:
        """更新Construct2精灵坐标"""
        try:
            # 备份原文件
            self.backup_manager.create_backup(self.config.runtime_file)
            
            runtime_path = os.path.join(self.project_path, self.config.runtime_file)
            with open(runtime_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 更新坐标
            pattern = r'(\["images/fish-sheet0\.png",\s*\d+,\s*)(\d+),\s*(\d+),\s*(\d+),\s*(\d+)(\s*,\s*[^\]]+\])'
            matches = list(re.finditer(pattern, content))
            
            updated_content = content
            for i, match in enumerate(matches):
                if i < len(self.sprites):
                    sprite = self.sprites[i]
                    new_coords = f"{match.group(1)}{sprite.x}, {sprite.y}, {sprite.width}, {sprite.height}{match.group(6)}"
                    updated_content = updated_content.replace(match.group(0), new_coords)
            
            # 保存更新后的文件
            with open(runtime_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            # 更新缓存版本
            self._update_cache_version()
            
            print(f"✅ 已更新 {len(self.sprites)} 个精灵坐标")
            return True
            
        except Exception as e:
            print(f"❌ 更新精灵坐标失败: {e}")
            return False
    
    def _update_cache_version(self):
        """更新缓存版本"""
        try:
            cache_path = os.path.join(self.project_path, self.config.cache_file)
            if not os.path.exists(cache_path):
                return
            
            with open(cache_path, 'r') as f:
                content = f.read()
            
            # 更新版本号
            timestamp = int(time.time())
            version_pattern = r'# Version: ([\d\.]+)'
            match = re.search(version_pattern, content)
            
            if match:
                old_version = match.group(1)
                version_parts = old_version.split('.')
                version_parts[-1] = str(int(version_parts[-1]) + 1)
                new_version = '.'.join(version_parts)
            else:
                new_version = "1.0.1"
            
            updated_content = re.sub(
                r'# Version: [\d\.]+\n# Last Updated: [^\n]+',
                f'# Version: {new_version}\n# Last Updated: {time.strftime("%Y-%m-%d")} - Sprite toolkit update',
                content
            )
            
            with open(cache_path, 'w') as f:
                f.write(updated_content)
            
            print(f"✅ 缓存版本已更新到 {new_version}")
            
        except Exception as e:
            print(f"⚠️ 更新缓存版本失败: {e}")

class BackupManager:
    """备份管理器"""
    
    def __init__(self, project_path: str):
        self.project_path = project_path
        self.backup_dir = os.path.join(project_path, "backup_sprites")
        
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
    
    def create_backup(self, file_path: str) -> str:
        """创建文件备份"""
        try:
            full_path = os.path.join(self.project_path, file_path)
            if not os.path.exists(full_path):
                return ""
            
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = os.path.basename(file_path)
            name, ext = os.path.splitext(filename)
            backup_name = f"{name}_backup_{timestamp}{ext}"
            backup_path = os.path.join(self.backup_dir, backup_name)
            
            shutil.copy2(full_path, backup_path)
            print(f"📦 已备份: {backup_name}")
            return backup_path
            
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return ""
    
    def list_backups(self) -> List[str]:
        """列出所有备份文件"""
        if not os.path.exists(self.backup_dir):
            return []
        
        return [f for f in os.listdir(self.backup_dir) if os.path.isfile(os.path.join(self.backup_dir, f))]
    
    def restore_backup(self, backup_name: str, target_path: str) -> bool:
        """恢复备份文件"""
        try:
            backup_path = os.path.join(self.backup_dir, backup_name)
            target_full_path = os.path.join(self.project_path, target_path)
            
            if not os.path.exists(backup_path):
                print(f"❌ 备份文件不存在: {backup_name}")
                return False
            
            shutil.copy2(backup_path, target_full_path)
            print(f"✅ 已恢复备份: {backup_name} -> {target_path}")
            return True
            
        except Exception as e:
            print(f"❌ 恢复备份失败: {e}")
            return False
