<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 Sprite Toolkit - Advanced Sprite Sheet Editor</title>
    <link rel="stylesheet" href="web/style.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1>🎨 Sprite Toolkit</h1>
                <div class="header-actions">
                    <div class="language-switcher">
                        <button id="langEn" class="btn btn-lang active" data-lang="en">EN</button>
                        <button id="langZh" class="btn btn-lang" data-lang="zh">中文</button>
                    </div>
                    <button id="loadProject" class="btn btn-primary" data-i18n="loadProject">Load Project</button>
                    <button id="saveProject" class="btn btn-success" data-i18n="saveChanges">Save Changes</button>
                    <div class="engine-indicator" id="engineIndicator">
                        <span class="engine-label" data-i18n="engine">Engine:</span>
                        <span class="engine-name" id="engineName" data-i18n="notDetected">Not Detected</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-section">
                    <h3 data-i18n="tools">🔧 Tools</h3>
                    <div class="tool-buttons">
                        <button id="autoDetect" class="btn btn-tool" data-i18n="autoDetect">🔍 Auto Detect</button>
                        <button id="manualMode" class="btn btn-tool" data-i18n="manualMode">✏️ Manual Mode</button>
                        <button id="gridMode" class="btn btn-tool" data-i18n="gridMode">📐 Grid Mode</button>
                        <button id="resetLayout" class="btn btn-tool" data-i18n="resetLayout">🔄 Reset</button>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3 data-i18n="spriteList">📊 Sprite List</h3>
                    <div id="spriteList" class="sprite-list">
                        <!-- Sprite items will be populated here -->
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3 data-i18n="settings">⚙️ Settings</h3>
                    <div class="settings-panel">
                        <label>
                            <span data-i18n="gridColumns">Grid Columns:</span>
                            <input type="number" id="gridCols" value="3" min="1" max="10">
                        </label>
                        <label>
                            <span data-i18n="gridRows">Grid Rows:</span>
                            <input type="number" id="gridRows" value="3" min="1" max="10">
                        </label>
                        <label>
                            <span data-i18n="snapToGrid">Snap to Grid:</span>
                            <input type="checkbox" id="snapToGrid">
                        </label>
                    </div>
                </div>
            </aside>

            <!-- Canvas Area -->
            <section class="canvas-area">
                <div class="canvas-toolbar">
                    <div class="toolbar-group">
                        <button id="zoomIn" class="btn btn-sm" data-i18n="zoomIn">🔍+</button>
                        <button id="zoomOut" class="btn btn-sm" data-i18n="zoomOut">🔍-</button>
                        <button id="zoomFit" class="btn btn-sm" data-i18n="zoomFit">📏 Fit</button>
                        <span id="zoomLevel" class="zoom-indicator">100%</span>
                    </div>

                    <div class="toolbar-group">
                        <button id="showGrid" class="btn btn-sm toggle" data-i18n="showGrid">📐 Grid</button>
                        <button id="showLabels" class="btn btn-sm toggle active" data-i18n="showLabels">🏷️ Labels</button>
                        <button id="showBounds" class="btn btn-sm toggle active" data-i18n="showBounds">📦 Bounds</button>
                    </div>
                </div>

                <div class="canvas-container" id="canvasContainer">
                    <canvas id="mainCanvas" width="1024" height="1024"></canvas>
                    <div id="spriteHandles" class="sprite-handles"></div>
                </div>

                <div class="canvas-info">
                    <div class="info-group">
                        <span>Selected: <span id="selectedSprite">None</span></span>
                        <span>Position: <span id="spritePosition">-</span></span>
                        <span>Size: <span id="spriteSize">-</span></span>
                    </div>
                </div>
            </section>

            <!-- Properties Panel -->
            <aside class="properties-panel">
                <div class="panel-section">
                    <h3 data-i18n="spriteProperties">📝 Sprite Properties</h3>
                    <div id="spriteProperties" class="properties-form">
                        <label>
                            <span data-i18n="name">Name:</span>
                            <input type="text" id="spriteName" placeholder="Sprite name">
                        </label>
                        <label>
                            <span>X:</span>
                            <input type="number" id="spriteX" min="0">
                        </label>
                        <label>
                            <span>Y:</span>
                            <input type="number" id="spriteY" min="0">
                        </label>
                        <label>
                            <span>Width:</span>
                            <input type="number" id="spriteWidth" min="1">
                        </label>
                        <label>
                            <span>Height:</span>
                            <input type="number" id="spriteHeight" min="1">
                        </label>
                        <button id="applyProperties" class="btn btn-primary" data-i18n="apply">Apply</button>
                    </div>
                </div>

                <div class="panel-section">
                    <h3 data-i18n="preview">🖼️ Preview</h3>
                    <div id="spritePreview" class="sprite-preview">
                        <div class="preview-placeholder" data-i18n="selectSprite">Select a sprite to preview</div>
                    </div>
                </div>

                <div class="panel-section">
                    <h3 data-i18n="export">📤 Export</h3>
                    <div class="export-options">
                        <button id="exportPython" class="btn btn-export" data-i18n="pythonCode">🐍 Python Code</button>
                        <button id="exportJSON" class="btn btn-export" data-i18n="jsonConfig">📄 JSON Config</button>
                        <button id="exportCSS" class="btn btn-export" data-i18n="cssSprites">🎨 CSS Sprites</button>
                        <button id="exportJS" class="btn btn-export" data-i18n="jsObject">📜 JavaScript</button>
                        <button id="generateVerification" class="btn btn-export" data-i18n="verification">✅ Verification</button>
                    </div>
                </div>

                <div class="panel-section">
                    <h3 data-i18n="backup">💾 Backup</h3>
                    <div class="backup-options">
                        <button id="listBackups" class="btn btn-backup" data-i18n="listBackups">📋 List Backups</button>
                        <select id="backupSelect" class="backup-select" style="display: none;">
                            <option value="" data-i18n="selectBackup">Select backup...</option>
                        </select>
                        <button id="restoreBackup" class="btn btn-backup" style="display: none;" data-i18n="restore">🔄 Restore</button>
                    </div>
                </div>
            </aside>
        </main>

        <!-- Status Bar -->
        <footer class="status-bar">
            <div class="status-content">
                <span id="statusMessage">Ready</span>
                <div class="status-indicators">
                    <span id="spriteCount">0 sprites</span>
                    <span id="imageSize">No image</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- Modals -->
    <div id="loadProjectModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Load Project</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <label>
                    <span data-i18n="projectPath">Project Path:</span>
                    <input type="text" id="projectPath" value="../" placeholder="Path to project directory">
                    <button type="button" id="browseProject" class="btn btn-sm" style="margin-left: 0.5rem;" data-i18n="browse">Browse</button>
                </label>
                <label>
                    <span data-i18n="spriteSheet">Sprite Sheet:</span>
                    <input type="text" id="spriteSheetPath" placeholder="images/sprite-sheet.png">
                    <button type="button" id="browseSpriteSheet" class="btn btn-sm" style="margin-left: 0.5rem;" data-i18n="browse">Browse</button>
                </label>
                <div class="project-info" id="projectInfo" style="margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 5px; display: none;">
                    <h4 data-i18n="detectedFiles">Detected Files:</h4>
                    <ul id="detectedFilesList"></ul>
                </div>
            </div>
            <div class="modal-footer">
                <button id="cancelLoad" class="btn btn-secondary" data-i18n="cancel">Cancel</button>
                <button id="confirmLoad" class="btn btn-primary" data-i18n="load">Load</button>
            </div>
        </div>
    </div>

    <div id="exportModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Export Configuration</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <textarea id="exportContent" readonly></textarea>
            </div>
            <div class="modal-footer">
                <button id="copyExport" class="btn btn-primary">Copy to Clipboard</button>
                <button class="modal-close btn btn-secondary">Close</button>
            </div>
        </div>
    </div>

    <div id="verificationModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>Verification Image</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <img id="verificationImage" alt="Verification" style="max-width: 100%; height: auto;">
            </div>
            <div class="modal-footer">
                <button class="modal-close btn btn-secondary">Close</button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">Processing...</div>
    </div>

    <script src="web/app.js"></script>
</body>
</html>
