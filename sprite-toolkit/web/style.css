/* 🎨 Sprite Toolkit - Modern UI Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f5f5;
    color: #333;
    overflow: hidden;
}

/* App Layout */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.header-content h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.engine-indicator {
    background: rgba(255,255,255,0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
}

.engine-label {
    opacity: 0.8;
    margin-right: 0.5rem;
}

.engine-name {
    font-weight: 600;
}

/* Main Content */
.app-main {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: white;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
    padding: 1rem;
}

.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-section h3 {
    font-size: 1rem;
    margin-bottom: 1rem;
    color: #555;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 0.5rem;
}

.tool-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.sprite-list {
    max-height: 300px;
    overflow-y: auto;
}

.sprite-item {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.sprite-item:hover {
    background: #e9ecef;
    border-color: #dee2e6;
}

.sprite-item.selected {
    background: #e3f2fd;
    border-color: #2196f3;
    color: #1976d2;
}

.sprite-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.sprite-item-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.sprite-item-id {
    background: #6c757d;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
}

.sprite-item-coords {
    font-size: 0.8rem;
    color: #666;
}

.settings-panel label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.settings-panel input[type="number"] {
    width: 60px;
    padding: 0.25rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.settings-panel input[type="checkbox"] {
    transform: scale(1.2);
}

/* Canvas Area */
.canvas-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #fafafa;
}

.canvas-toolbar {
    background: white;
    border-bottom: 1px solid #e0e0e0;
    padding: 0.75rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.toolbar-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.zoom-indicator {
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
    min-width: 50px;
    text-align: center;
}

.canvas-container {
    flex: 1;
    position: relative;
    overflow: auto;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
}

#mainCanvas {
    border: 2px solid #ddd;
    cursor: crosshair;
    max-width: 100%;
    max-height: 100%;
}

.sprite-handles {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
}

.sprite-handle {
    position: absolute;
    border: 2px solid #2196f3;
    background: rgba(33, 150, 243, 0.1);
    pointer-events: auto;
    cursor: move;
}

.sprite-handle.selected {
    border-color: #f44336;
    background: rgba(244, 67, 54, 0.1);
}

.sprite-handle-label {
    position: absolute;
    top: 5px;
    left: 5px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.canvas-info {
    background: white;
    border-top: 1px solid #e0e0e0;
    padding: 0.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.info-group {
    display: flex;
    gap: 2rem;
}

.info-group span {
    color: #666;
}

/* Properties Panel */
.properties-panel {
    width: 300px;
    background: white;
    border-left: 1px solid #e0e0e0;
    overflow-y: auto;
    padding: 1rem;
}

.panel-section {
    margin-bottom: 2rem;
}

.panel-section h3 {
    font-size: 1rem;
    margin-bottom: 1rem;
    color: #555;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 0.5rem;
}

.properties-form label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.properties-form input {
    width: 120px;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.sprite-preview {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.preview-placeholder {
    color: #999;
    font-style: italic;
}

.sprite-preview img {
    max-width: 100%;
    max-height: 150px;
    border-radius: 4px;
}

.export-options, .backup-options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.backup-select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.btn-primary {
    background: #2196f3;
    color: white;
}

.btn-primary:hover {
    background: #1976d2;
}

.btn-success {
    background: #4caf50;
    color: white;
}

.btn-success:hover {
    background: #388e3c;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-tool {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

.btn-tool:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.btn-tool.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.btn-export {
    background: #ff9800;
    color: white;
    font-size: 0.8rem;
}

.btn-export:hover {
    background: #f57c00;
}

.btn-backup {
    background: #9c27b0;
    color: white;
    font-size: 0.8rem;
}

.btn-backup:hover {
    background: #7b1fa2;
}

.toggle {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
}

.toggle.active {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

/* Status Bar */
.status-bar {
    background: #343a40;
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.status-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.status-indicators {
    display: flex;
    gap: 2rem;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-content.large {
    max-width: 800px;
}

.modal-header {
    background: #f8f9fa;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.1rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #495057;
}

.modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-body label {
    display: block;
    margin-bottom: 1rem;
}

.modal-body label span {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.modal-body input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.modal-body textarea {
    width: 100%;
    height: 300px;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9rem;
    resize: vertical;
}

.modal-footer {
    background: #f8f9fa;
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

/* Loading Overlay */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.9);
    z-index: 2000;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2196f3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 1.1rem;
    color: #666;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .sidebar {
        width: 250px;
    }
    
    .properties-panel {
        width: 250px;
    }
}

@media (max-width: 900px) {
    .app-main {
        flex-direction: column;
    }
    
    .sidebar, .properties-panel {
        width: 100%;
        height: auto;
        max-height: 200px;
    }
    
    .canvas-area {
        flex: 1;
        min-height: 400px;
    }
}
