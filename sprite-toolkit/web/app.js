// 🎨 Sprite Toolkit - Main Application Logic

class SpriteToolkitApp {
    constructor() {
        this.sprites = [];
        this.selectedSpriteId = null;
        this.canvas = null;
        this.ctx = null;
        this.spriteImage = null;
        this.zoom = 1;
        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };
        
        this.init();
    }
    
    init() {
        this.setupCanvas();
        this.setupEventListeners();
        this.setupModals();
        this.updateUI();
    }
    
    setupCanvas() {
        this.canvas = document.getElementById('mainCanvas');
        this.ctx = this.canvas.getContext('2d');
        
        // Canvas event listeners
        this.canvas.addEventListener('click', (e) => this.handleCanvasClick(e));
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
    }
    
    setupEventListeners() {
        // Header actions
        document.getElementById('loadProject').addEventListener('click', () => this.showLoadProjectModal());
        document.getElementById('saveProject').addEventListener('click', () => this.saveProject());
        
        // Tool buttons
        document.getElementById('autoDetect').addEventListener('click', () => this.autoDetectSprites());
        document.getElementById('manualMode').addEventListener('click', () => this.setManualMode());
        document.getElementById('gridMode').addEventListener('click', () => this.setGridMode());
        document.getElementById('resetLayout').addEventListener('click', () => this.resetLayout());
        
        // Canvas toolbar
        document.getElementById('zoomIn').addEventListener('click', () => this.zoomIn());
        document.getElementById('zoomOut').addEventListener('click', () => this.zoomOut());
        document.getElementById('zoomFit').addEventListener('click', () => this.zoomFit());
        
        // Toggle buttons
        document.getElementById('showGrid').addEventListener('click', (e) => this.toggleGrid(e));
        document.getElementById('showLabels').addEventListener('click', (e) => this.toggleLabels(e));
        document.getElementById('showBounds').addEventListener('click', (e) => this.toggleBounds(e));
        
        // Properties
        document.getElementById('applyProperties').addEventListener('click', () => this.applyProperties());
        
        // Export buttons
        document.getElementById('exportPython').addEventListener('click', () => this.exportConfig('python'));
        document.getElementById('exportJSON').addEventListener('click', () => this.exportConfig('json'));
        document.getElementById('generateVerification').addEventListener('click', () => this.generateVerification());
        
        // Backup buttons
        document.getElementById('listBackups').addEventListener('click', () => this.listBackups());
        document.getElementById('restoreBackup').addEventListener('click', () => this.restoreBackup());
        
        // Property inputs
        ['spriteX', 'spriteY', 'spriteWidth', 'spriteHeight'].forEach(id => {
            document.getElementById(id).addEventListener('input', () => this.updateSelectedSprite());
        });
    }
    
    setupModals() {
        // Modal close handlers
        document.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                this.hideModal(modal);
            });
        });
        
        // Load project modal
        document.getElementById('confirmLoad').addEventListener('click', () => this.loadProject());
        document.getElementById('cancelLoad').addEventListener('click', () => {
            this.hideModal(document.getElementById('loadProjectModal'));
        });
        
        // Export modal
        document.getElementById('copyExport').addEventListener('click', () => this.copyToClipboard());
    }
    
    async loadProject() {
        const projectPath = document.getElementById('projectPath').value;
        const spriteSheetPath = document.getElementById('spriteSheetPath').value;
        
        this.showLoading('Loading project...');
        
        try {
            // Initialize project
            const initResponse = await fetch('/api/init', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ project_path: projectPath })
            });
            
            const initResult = await initResponse.json();
            if (!initResult.success) {
                throw new Error(initResult.error);
            }
            
            // Update engine indicator
            document.getElementById('engineName').textContent = initResult.config.engine;
            
            // Load sprite sheet
            const loadResponse = await fetch('/api/load_sprite_sheet', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ sprite_path: spriteSheetPath || initResult.config.sprite_sheet_path })
            });
            
            const loadResult = await loadResponse.json();
            if (!loadResult.success) {
                throw new Error(loadResult.error);
            }
            
            // Load image
            this.spriteImage = new Image();
            this.spriteImage.onload = () => {
                this.canvas.width = this.spriteImage.width;
                this.canvas.height = this.spriteImage.height;
                this.drawCanvas();
                this.zoomFit();
            };
            this.spriteImage.src = loadResult.image_data;
            
            // Update status
            document.getElementById('imageSize').textContent = `${loadResult.size[0]}×${loadResult.size[1]}`;
            
            // Analyze sprites
            await this.analyzeSprites(false); // Load current sprites
            
            this.hideModal(document.getElementById('loadProjectModal'));
            this.setStatus('Project loaded successfully');
            
        } catch (error) {
            this.setStatus(`Error: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    async analyzeSprites(autoDetect = true) {
        this.showLoading('Analyzing sprites...');
        
        try {
            const response = await fetch('/api/analyze_sprites', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ auto_detect: autoDetect })
            });
            
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.error);
            }
            
            this.sprites = result.sprites;
            this.updateSpriteList();
            this.drawCanvas();
            this.setStatus(`Found ${this.sprites.length} sprites`);
            
        } catch (error) {
            this.setStatus(`Error: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    async saveProject() {
        if (this.sprites.length === 0) {
            this.setStatus('No sprites to save', 'error');
            return;
        }
        
        this.showLoading('Saving project...');
        
        try {
            const response = await fetch('/api/update_sprites', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ sprites: this.sprites })
            });
            
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.error);
            }
            
            this.setStatus('Project saved successfully');
            
        } catch (error) {
            this.setStatus(`Error: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    drawCanvas() {
        if (!this.spriteImage) return;
        
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw sprite image
        this.ctx.drawImage(this.spriteImage, 0, 0);
        
        // Draw sprite bounds
        if (document.getElementById('showBounds').classList.contains('active')) {
            this.sprites.forEach((sprite, index) => {
                this.ctx.strokeStyle = sprite.id === this.selectedSpriteId ? '#f44336' : '#2196f3';
                this.ctx.lineWidth = 2;
                this.ctx.strokeRect(sprite.x, sprite.y, sprite.width, sprite.height);
                
                // Draw labels
                if (document.getElementById('showLabels').classList.contains('active')) {
                    this.ctx.fillStyle = 'rgba(0,0,0,0.8)';
                    this.ctx.fillRect(sprite.x + 5, sprite.y + 5, 30, 20);
                    this.ctx.fillStyle = 'white';
                    this.ctx.font = '14px Arial';
                    this.ctx.fillText(sprite.id + 1, sprite.x + 10, sprite.y + 18);
                }
            });
        }
        
        // Draw grid
        if (document.getElementById('showGrid').classList.contains('active')) {
            this.drawGrid();
        }
    }
    
    drawGrid() {
        const gridSize = 50;
        this.ctx.strokeStyle = 'rgba(0,0,0,0.1)';
        this.ctx.lineWidth = 1;
        
        for (let x = 0; x <= this.canvas.width; x += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }
        
        for (let y = 0; y <= this.canvas.height; y += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }
    
    handleCanvasClick(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = (e.clientX - rect.left) / this.zoom;
        const y = (e.clientY - rect.top) / this.zoom;
        
        // Find clicked sprite
        let clickedSprite = null;
        for (let i = this.sprites.length - 1; i >= 0; i--) {
            const sprite = this.sprites[i];
            if (x >= sprite.x && x <= sprite.x + sprite.width &&
                y >= sprite.y && y <= sprite.y + sprite.height) {
                clickedSprite = sprite;
                break;
            }
        }
        
        if (clickedSprite) {
            this.selectSprite(clickedSprite.id);
        } else {
            // Create new sprite or move selected sprite
            if (this.selectedSpriteId !== null) {
                const sprite = this.sprites.find(s => s.id === this.selectedSpriteId);
                if (sprite) {
                    sprite.x = Math.round(x);
                    sprite.y = Math.round(y);
                    this.updatePropertiesPanel();
                    this.drawCanvas();
                }
            }
        }
    }
    
    selectSprite(id) {
        this.selectedSpriteId = id;
        this.updateSpriteList();
        this.updatePropertiesPanel();
        this.updateSpritePreview();
        this.drawCanvas();
    }
    
    updateSpriteList() {
        const container = document.getElementById('spriteList');
        container.innerHTML = '';
        
        this.sprites.forEach(sprite => {
            const item = document.createElement('div');
            item.className = `sprite-item ${sprite.id === this.selectedSpriteId ? 'selected' : ''}`;
            item.onclick = () => this.selectSprite(sprite.id);
            
            item.innerHTML = `
                <div class="sprite-item-header">
                    <span class="sprite-item-name">${sprite.name}</span>
                    <span class="sprite-item-id">${sprite.id + 1}</span>
                </div>
                <div class="sprite-item-coords">
                    ${sprite.x}, ${sprite.y} (${sprite.width}×${sprite.height})
                </div>
            `;
            
            container.appendChild(item);
        });
        
        document.getElementById('spriteCount').textContent = `${this.sprites.length} sprites`;
    }
    
    updatePropertiesPanel() {
        const sprite = this.sprites.find(s => s.id === this.selectedSpriteId);
        
        if (sprite) {
            document.getElementById('spriteName').value = sprite.name;
            document.getElementById('spriteX').value = sprite.x;
            document.getElementById('spriteY').value = sprite.y;
            document.getElementById('spriteWidth').value = sprite.width;
            document.getElementById('spriteHeight').value = sprite.height;
            
            document.getElementById('selectedSprite').textContent = sprite.name;
            document.getElementById('spritePosition').textContent = `${sprite.x}, ${sprite.y}`;
            document.getElementById('spriteSize').textContent = `${sprite.width}×${sprite.height}`;
        } else {
            document.getElementById('selectedSprite').textContent = 'None';
            document.getElementById('spritePosition').textContent = '-';
            document.getElementById('spriteSize').textContent = '-';
        }
    }
    
    updateSelectedSprite() {
        const sprite = this.sprites.find(s => s.id === this.selectedSpriteId);
        if (!sprite) return;
        
        sprite.x = parseInt(document.getElementById('spriteX').value) || 0;
        sprite.y = parseInt(document.getElementById('spriteY').value) || 0;
        sprite.width = parseInt(document.getElementById('spriteWidth').value) || 1;
        sprite.height = parseInt(document.getElementById('spriteHeight').value) || 1;
        
        this.updateSpriteList();
        this.drawCanvas();
    }
    
    applyProperties() {
        const sprite = this.sprites.find(s => s.id === this.selectedSpriteId);
        if (!sprite) return;
        
        sprite.name = document.getElementById('spriteName').value || `Sprite ${sprite.id + 1}`;
        this.updateSelectedSprite();
        this.updateSpritePreview();
        this.setStatus('Properties applied');
    }
    
    async updateSpritePreview() {
        const sprite = this.sprites.find(s => s.id === this.selectedSpriteId);
        const previewContainer = document.getElementById('spritePreview');
        
        if (!sprite || !this.spriteImage) {
            previewContainer.innerHTML = '<div class="preview-placeholder">Select a sprite to preview</div>';
            return;
        }
        
        // Create canvas for sprite extraction
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = sprite.width;
        canvas.height = sprite.height;
        
        // Draw sprite
        ctx.drawImage(this.spriteImage, 
            sprite.x, sprite.y, sprite.width, sprite.height,
            0, 0, sprite.width, sprite.height
        );
        
        // Create image element
        const img = document.createElement('img');
        img.src = canvas.toDataURL();
        img.alt = sprite.name;
        
        previewContainer.innerHTML = '';
        previewContainer.appendChild(img);
    }
    
    // Tool methods
    async autoDetectSprites() {
        await this.analyzeSprites(true);
    }
    
    setManualMode() {
        this.setStatus('Manual mode activated - Click to position sprites');
    }
    
    setGridMode() {
        const cols = parseInt(document.getElementById('gridCols').value) || 3;
        const rows = parseInt(document.getElementById('gridRows').value) || 3;
        
        if (!this.spriteImage) {
            this.setStatus('Please load a sprite sheet first', 'error');
            return;
        }
        
        const spriteWidth = Math.floor(this.spriteImage.width / cols);
        const spriteHeight = Math.floor(this.spriteImage.height / rows);
        
        this.sprites = [];
        for (let i = 0; i < rows * cols; i++) {
            const row = Math.floor(i / cols);
            const col = i % cols;
            
            this.sprites.push({
                id: i,
                x: col * spriteWidth,
                y: row * spriteHeight,
                width: spriteWidth,
                height: spriteHeight,
                name: `Sprite ${i + 1}`
            });
        }
        
        this.updateSpriteList();
        this.drawCanvas();
        this.setStatus(`Generated ${cols}×${rows} grid layout`);
    }
    
    resetLayout() {
        this.sprites = [];
        this.selectedSpriteId = null;
        this.updateSpriteList();
        this.updatePropertiesPanel();
        this.drawCanvas();
        this.setStatus('Layout reset');
    }
    
    // Zoom methods
    zoomIn() {
        this.zoom = Math.min(this.zoom * 1.2, 5);
        this.updateZoom();
    }
    
    zoomOut() {
        this.zoom = Math.max(this.zoom / 1.2, 0.1);
        this.updateZoom();
    }
    
    zoomFit() {
        if (!this.spriteImage) return;
        
        const container = document.getElementById('canvasContainer');
        const containerWidth = container.clientWidth - 40;
        const containerHeight = container.clientHeight - 40;
        
        const scaleX = containerWidth / this.spriteImage.width;
        const scaleY = containerHeight / this.spriteImage.height;
        
        this.zoom = Math.min(scaleX, scaleY, 1);
        this.updateZoom();
    }
    
    updateZoom() {
        this.canvas.style.transform = `scale(${this.zoom})`;
        document.getElementById('zoomLevel').textContent = `${Math.round(this.zoom * 100)}%`;
    }
    
    // Toggle methods
    toggleGrid(e) {
        e.target.classList.toggle('active');
        this.drawCanvas();
    }
    
    toggleLabels(e) {
        e.target.classList.toggle('active');
        this.drawCanvas();
    }
    
    toggleBounds(e) {
        e.target.classList.toggle('active');
        this.drawCanvas();
    }
    
    // Export methods
    async exportConfig(format) {
        if (this.sprites.length === 0) {
            this.setStatus('No sprites to export', 'error');
            return;
        }
        
        try {
            const response = await fetch('/api/export_config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ format })
            });
            
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.error);
            }
            
            document.getElementById('exportContent').value = 
                typeof result.config === 'string' ? result.config : JSON.stringify(result.config, null, 2);
            
            this.showModal(document.getElementById('exportModal'));
            
        } catch (error) {
            this.setStatus(`Export error: ${error.message}`, 'error');
        }
    }
    
    async generateVerification() {
        if (this.sprites.length === 0) {
            this.setStatus('No sprites to verify', 'error');
            return;
        }
        
        this.showLoading('Generating verification image...');
        
        try {
            const response = await fetch('/api/generate_verification', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({})
            });
            
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.error);
            }
            
            document.getElementById('verificationImage').src = result.verification_image;
            this.showModal(document.getElementById('verificationModal'));
            
        } catch (error) {
            this.setStatus(`Verification error: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    // Backup methods
    async listBackups() {
        try {
            const response = await fetch('/api/backup/list');
            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.error);
            }
            
            const select = document.getElementById('backupSelect');
            select.innerHTML = '<option value="">Select backup...</option>';
            
            result.backups.forEach(backup => {
                const option = document.createElement('option');
                option.value = backup;
                option.textContent = backup;
                select.appendChild(option);
            });
            
            select.style.display = 'block';
            document.getElementById('restoreBackup').style.display = 'block';
            
        } catch (error) {
            this.setStatus(`Backup list error: ${error.message}`, 'error');
        }
    }
    
    async restoreBackup() {
        const backupName = document.getElementById('backupSelect').value;
        if (!backupName) {
            this.setStatus('Please select a backup', 'error');
            return;
        }
        
        try {
            const response = await fetch('/api/backup/restore', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ backup_name: backupName })
            });
            
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.error);
            }
            
            this.setStatus('Backup restored successfully');
            
        } catch (error) {
            this.setStatus(`Restore error: ${error.message}`, 'error');
        }
    }
    
    // Utility methods
    copyToClipboard() {
        const content = document.getElementById('exportContent');
        content.select();
        document.execCommand('copy');
        this.setStatus('Copied to clipboard');
    }
    
    showModal(modal) {
        modal.classList.add('show');
    }
    
    hideModal(modal) {
        modal.classList.remove('show');
    }
    
    showLoadProjectModal() {
        this.showModal(document.getElementById('loadProjectModal'));
    }
    
    showLoading(message = 'Loading...') {
        document.querySelector('.loading-text').textContent = message;
        document.getElementById('loadingOverlay').classList.add('show');
    }
    
    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('show');
    }
    
    setStatus(message, type = 'info') {
        document.getElementById('statusMessage').textContent = message;
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
    
    updateUI() {
        this.updateSpriteList();
        this.updatePropertiesPanel();
        this.drawCanvas();
    }
    
    // Mouse handling for dragging
    handleMouseDown(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = (e.clientX - rect.left) / this.zoom;
        const y = (e.clientY - rect.top) / this.zoom;
        
        // Check if clicking on a sprite
        for (let sprite of this.sprites) {
            if (x >= sprite.x && x <= sprite.x + sprite.width &&
                y >= sprite.y && y <= sprite.y + sprite.height) {
                this.isDragging = true;
                this.selectSprite(sprite.id);
                this.dragOffset = {
                    x: x - sprite.x,
                    y: y - sprite.y
                };
                break;
            }
        }
    }
    
    handleMouseMove(e) {
        if (!this.isDragging || this.selectedSpriteId === null) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const x = (e.clientX - rect.left) / this.zoom;
        const y = (e.clientY - rect.top) / this.zoom;
        
        const sprite = this.sprites.find(s => s.id === this.selectedSpriteId);
        if (sprite) {
            sprite.x = Math.round(x - this.dragOffset.x);
            sprite.y = Math.round(y - this.dragOffset.y);
            
            this.updatePropertiesPanel();
            this.drawCanvas();
        }
    }
    
    handleMouseUp(e) {
        if (this.isDragging) {
            this.isDragging = false;
            this.updateSpriteList();
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.spriteApp = new SpriteToolkitApp();
});
