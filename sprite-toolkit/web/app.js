// 🎨 Sprite Toolkit - Main Application Logic

// 国际化文本
const i18nTexts = {
    en: {
        // Header
        loadProject: "Load Project",
        saveChanges: "Save Changes",
        engine: "Engine:",
        notDetected: "Not Detected",

        // Tools
        tools: "🔧 Tools",
        autoDetect: "🔍 Auto Detect",
        manualMode: "✏️ Manual Mode",
        gridMode: "📐 Grid Mode",
        resetLayout: "🔄 Reset",

        // Sidebar
        spriteList: "📊 Sprite List",
        settings: "⚙️ Settings",
        gridColumns: "Grid Columns:",
        gridRows: "Grid Rows:",
        snapToGrid: "Snap to Grid:",

        // Canvas
        zoomIn: "🔍+",
        zoomOut: "🔍-",
        zoomFit: "📏 Fit",
        showGrid: "📐 Grid",
        showLabels: "🏷️ Labels",
        showBounds: "📦 Bounds",

        // Properties
        spriteProperties: "📝 Sprite Properties",
        name: "Name:",
        apply: "Apply",
        preview: "🖼️ Preview",
        export: "📤 Export",
        backup: "💾 Backup",
        pythonCode: "🐍 Python Code",
        jsonConfig: "📄 JSON Config",
        cssSprites: "🎨 CSS Sprites",
        jsObject: "📜 JavaScript",
        verification: "✅ Verification",
        listBackups: "📋 List Backups",
        selectBackup: "Select backup...",
        restore: "🔄 Restore",

        // Status
        ready: "Ready",
        loading: "Loading...",
        projectLoaded: "Project loaded successfully",
        spritesSaved: "Sprites saved successfully",

        // Messages
        selectSprite: "Select a sprite to preview",
        noSprites: "No sprites to export",
        coordinatesGenerated: "Coordinates generated successfully",

        // Modal
        projectPath: "Project Path:",
        spriteSheet: "Sprite Sheet:",
        gameEngine: "Game Engine:",
        autoDetect: "Auto Detect",
        browse: "Browse",
        detectedFiles: "Detected Files:",
        cancel: "Cancel",
        load: "Load"
    },
    zh: {
        // Header
        loadProject: "加载项目",
        saveChanges: "保存更改",
        engine: "引擎:",
        notDetected: "未检测到",

        // Tools
        tools: "🔧 工具",
        autoDetect: "🔍 自动检测",
        manualMode: "✏️ 手动模式",
        gridMode: "📐 网格模式",
        resetLayout: "🔄 重置",

        // Sidebar
        spriteList: "📊 精灵列表",
        settings: "⚙️ 设置",
        gridColumns: "网格列数:",
        gridRows: "网格行数:",
        snapToGrid: "对齐网格:",

        // Canvas
        zoomIn: "🔍+",
        zoomOut: "🔍-",
        zoomFit: "📏 适应",
        showGrid: "📐 网格",
        showLabels: "🏷️ 标签",
        showBounds: "📦 边界",

        // Properties
        spriteProperties: "📝 精灵属性",
        name: "名称:",
        apply: "应用",
        preview: "🖼️ 预览",
        export: "📤 导出",
        backup: "💾 备份",
        pythonCode: "🐍 Python代码",
        jsonConfig: "📄 JSON配置",
        cssSprites: "🎨 CSS精灵图",
        jsObject: "📜 JavaScript",
        verification: "✅ 验证图",
        listBackups: "📋 列出备份",
        selectBackup: "选择备份...",
        restore: "🔄 恢复",

        // Status
        ready: "就绪",
        loading: "加载中...",
        projectLoaded: "项目加载成功",
        spritesSaved: "精灵保存成功",

        // Messages
        selectSprite: "选择一个精灵进行预览",
        noSprites: "没有精灵可导出",
        coordinatesGenerated: "坐标生成成功",

        // Modal
        projectPath: "项目路径:",
        spriteSheet: "精灵图:",
        gameEngine: "游戏引擎:",
        autoDetect: "自动检测",
        browse: "浏览",
        detectedFiles: "检测到的文件:",
        cancel: "取消",
        load: "加载"
    }
};

class SpriteToolkitApp {
    constructor() {
        this.sprites = [];
        this.selectedSpriteId = null;
        this.canvas = null;
        this.ctx = null;
        this.spriteImage = null;
        this.zoom = 1;
        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };
        this.currentLanguage = localStorage.getItem('spriteToolkitLang') || 'en';

        this.init();
    }
    
    init() {
        this.setupCanvas();
        this.setupEventListeners();
        this.setupModals();
        this.setupI18n();
        this.updateUI();
    }

    setupI18n() {
        // 设置语言切换按钮
        document.getElementById('langEn').addEventListener('click', () => this.switchLanguage('en'));
        document.getElementById('langZh').addEventListener('click', () => this.switchLanguage('zh'));

        // 应用当前语言
        this.applyLanguage();
    }

    switchLanguage(lang) {
        this.currentLanguage = lang;
        localStorage.setItem('spriteToolkitLang', lang);

        // 更新按钮状态
        document.querySelectorAll('.btn-lang').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.lang === lang);
        });

        // 应用新语言
        this.applyLanguage();
    }

    applyLanguage() {
        const texts = i18nTexts[this.currentLanguage];

        // 更新所有带有 data-i18n 属性的元素
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            if (texts[key]) {
                element.textContent = element.textContent.replace(/^[🔧📊⚙️🔍✏️📐🔄📝🖼️📤💾🏷️📦📏]+\s*/, '');
                const emoji = element.textContent.match(/^[🔧📊⚙️🔍✏️📐🔄📝🖼️📤💾🏷️📦📏]+/);
                element.textContent = (emoji ? emoji[0] + ' ' : '') + texts[key];
            }
        });

        // 更新按钮状态
        document.querySelectorAll('.btn-lang').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.lang === this.currentLanguage);
        });

        // 更新状态栏
        this.setStatus(texts.ready);
    }

    t(key) {
        return i18nTexts[this.currentLanguage][key] || key;
    }
    
    setupCanvas() {
        this.canvas = document.getElementById('mainCanvas');
        this.ctx = this.canvas.getContext('2d');
        
        // Canvas event listeners
        this.canvas.addEventListener('click', (e) => this.handleCanvasClick(e));
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
    }
    
    setupEventListeners() {
        // Header actions
        document.getElementById('loadProject').addEventListener('click', () => this.showLoadProjectModal());
        document.getElementById('saveProject').addEventListener('click', () => this.saveProject());
        
        // Tool buttons
        document.getElementById('autoDetect').addEventListener('click', () => this.autoDetectSprites());
        document.getElementById('manualMode').addEventListener('click', () => this.setManualMode());
        document.getElementById('gridMode').addEventListener('click', () => this.setGridMode());
        document.getElementById('resetLayout').addEventListener('click', () => this.resetLayout());
        
        // Canvas toolbar
        document.getElementById('zoomIn').addEventListener('click', () => this.zoomIn());
        document.getElementById('zoomOut').addEventListener('click', () => this.zoomOut());
        document.getElementById('zoomFit').addEventListener('click', () => this.zoomFit());
        
        // Toggle buttons
        document.getElementById('showGrid').addEventListener('click', (e) => this.toggleGrid(e));
        document.getElementById('showLabels').addEventListener('click', (e) => this.toggleLabels(e));
        document.getElementById('showBounds').addEventListener('click', (e) => this.toggleBounds(e));
        
        // Properties
        document.getElementById('applyProperties').addEventListener('click', () => this.applyProperties());
        
        // Export buttons
        document.getElementById('exportPython').addEventListener('click', () => this.exportConfig('python'));
        document.getElementById('exportJSON').addEventListener('click', () => this.exportConfig('json'));
        document.getElementById('exportCSS').addEventListener('click', () => this.exportConfig('css'));
        document.getElementById('exportJS').addEventListener('click', () => this.exportConfig('javascript'));
        document.getElementById('generateVerification').addEventListener('click', () => this.generateVerification());
        
        // Backup buttons
        document.getElementById('listBackups').addEventListener('click', () => this.listBackups());
        document.getElementById('restoreBackup').addEventListener('click', () => this.restoreBackup());
        
        // Property inputs
        ['spriteX', 'spriteY', 'spriteWidth', 'spriteHeight'].forEach(id => {
            document.getElementById(id).addEventListener('input', () => this.updateSelectedSprite());
        });
    }
    
    setupModals() {
        // Modal close handlers
        document.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                this.hideModal(modal);
            });
        });
        
        // Load project modal
        document.getElementById('confirmLoad').addEventListener('click', () => this.loadProject());
        document.getElementById('cancelLoad').addEventListener('click', () => {
            this.hideModal(document.getElementById('loadProjectModal'));
        });

        // Browse buttons
        document.getElementById('browseProject').addEventListener('click', () => this.browseProjectPath());
        document.getElementById('browseSpriteSheet').addEventListener('click', () => this.browseSpriteSheet());

        // Project path change detection
        document.getElementById('projectPath').addEventListener('input', () => this.detectProjectFiles());
        
        // Export modal
        document.getElementById('copyExport').addEventListener('click', () => this.copyToClipboard());
    }
    
    async loadProject() {
        const projectPath = document.getElementById('projectPath').value;
        const spriteSheetPath = document.getElementById('spriteSheetPath').value;
        const engineOverride = document.getElementById('engineOverride').value;

        this.showLoading('Loading project...');

        try {
            // Initialize project with optional engine override
            const initData = { project_path: projectPath };
            if (engineOverride) {
                initData.engine_override = engineOverride;
            }

            const initResponse = await fetch('/api/init', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(initData)
            });

            const initResult = await initResponse.json();
            if (!initResult.success) {
                throw new Error(initResult.error);
            }

            // Update engine indicator with override or detected engine
            const engineDisplay = engineOverride || initResult.config.engine;
            document.getElementById('engineName').textContent = engineDisplay;
            
            // Load sprite sheet
            const loadResponse = await fetch('/api/load_sprite_sheet', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ sprite_path: spriteSheetPath || initResult.config.sprite_sheet_path })
            });
            
            const loadResult = await loadResponse.json();
            if (!loadResult.success) {
                throw new Error(loadResult.error);
            }
            
            // Load image
            this.spriteImage = new Image();
            this.spriteImage.onload = () => {
                this.canvas.width = this.spriteImage.width;
                this.canvas.height = this.spriteImage.height;
                this.drawCanvas();
                this.zoomFit();
            };
            this.spriteImage.src = loadResult.image_data;
            
            // Update status
            document.getElementById('imageSize').textContent = `${loadResult.size[0]}×${loadResult.size[1]}`;
            
            // Analyze sprites
            await this.analyzeSprites(false); // Load current sprites
            
            this.hideModal(document.getElementById('loadProjectModal'));
            this.setStatus(this.t('projectLoaded'));
            
        } catch (error) {
            this.setStatus(`Error: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    async analyzeSprites(autoDetect = true) {
        this.showLoading('Analyzing sprites...');
        
        try {
            const response = await fetch('/api/analyze_sprites', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ auto_detect: autoDetect })
            });
            
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.error);
            }
            
            this.sprites = result.sprites;
            this.updateSpriteList();
            this.drawCanvas();
            this.setStatus(`Found ${this.sprites.length} sprites`);
            
        } catch (error) {
            this.setStatus(`Error: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    async saveProject() {
        if (this.sprites.length === 0) {
            this.setStatus('No sprites to save', 'error');
            return;
        }
        
        this.showLoading('Saving project...');
        
        try {
            const response = await fetch('/api/update_sprites', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ sprites: this.sprites })
            });
            
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.error);
            }
            
            this.setStatus(this.t('spritesSaved'));
            
        } catch (error) {
            this.setStatus(`Error: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    drawCanvas() {
        if (!this.spriteImage) return;
        
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw sprite image
        this.ctx.drawImage(this.spriteImage, 0, 0);
        
        // Draw sprite bounds
        if (document.getElementById('showBounds').classList.contains('active')) {
            this.sprites.forEach((sprite, index) => {
                this.ctx.strokeStyle = sprite.id === this.selectedSpriteId ? '#f44336' : '#2196f3';
                this.ctx.lineWidth = 2;
                this.ctx.strokeRect(sprite.x, sprite.y, sprite.width, sprite.height);
                
                // Draw labels
                if (document.getElementById('showLabels').classList.contains('active')) {
                    this.ctx.fillStyle = 'rgba(0,0,0,0.8)';
                    this.ctx.fillRect(sprite.x + 5, sprite.y + 5, 30, 20);
                    this.ctx.fillStyle = 'white';
                    this.ctx.font = '14px Arial';
                    this.ctx.fillText(sprite.id + 1, sprite.x + 10, sprite.y + 18);
                }
            });
        }
        
        // Draw grid
        if (document.getElementById('showGrid').classList.contains('active')) {
            this.drawGrid();
        }
    }
    
    drawGrid() {
        const gridSize = 50;
        this.ctx.strokeStyle = 'rgba(0,0,0,0.1)';
        this.ctx.lineWidth = 1;
        
        for (let x = 0; x <= this.canvas.width; x += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }
        
        for (let y = 0; y <= this.canvas.height; y += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }
    
    handleCanvasClick(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = (e.clientX - rect.left) / this.zoom;
        const y = (e.clientY - rect.top) / this.zoom;
        
        // Find clicked sprite
        let clickedSprite = null;
        for (let i = this.sprites.length - 1; i >= 0; i--) {
            const sprite = this.sprites[i];
            if (x >= sprite.x && x <= sprite.x + sprite.width &&
                y >= sprite.y && y <= sprite.y + sprite.height) {
                clickedSprite = sprite;
                break;
            }
        }
        
        if (clickedSprite) {
            this.selectSprite(clickedSprite.id);
        } else {
            // Create new sprite or move selected sprite
            if (this.selectedSpriteId !== null) {
                const sprite = this.sprites.find(s => s.id === this.selectedSpriteId);
                if (sprite) {
                    sprite.x = Math.round(x);
                    sprite.y = Math.round(y);
                    this.updatePropertiesPanel();
                    this.drawCanvas();
                }
            }
        }
    }
    
    selectSprite(id) {
        this.selectedSpriteId = id;
        this.updateSpriteList();
        this.updatePropertiesPanel();
        this.updateSpritePreview();
        this.drawCanvas();
    }
    
    updateSpriteList() {
        const container = document.getElementById('spriteList');
        container.innerHTML = '';
        
        this.sprites.forEach(sprite => {
            const item = document.createElement('div');
            item.className = `sprite-item ${sprite.id === this.selectedSpriteId ? 'selected' : ''}`;
            item.onclick = () => this.selectSprite(sprite.id);
            
            item.innerHTML = `
                <div class="sprite-item-header">
                    <span class="sprite-item-name">${sprite.name}</span>
                    <span class="sprite-item-id">${sprite.id + 1}</span>
                </div>
                <div class="sprite-item-coords">
                    ${sprite.x}, ${sprite.y} (${sprite.width}×${sprite.height})
                </div>
            `;
            
            container.appendChild(item);
        });
        
        document.getElementById('spriteCount').textContent = `${this.sprites.length} sprites`;
    }
    
    updatePropertiesPanel() {
        const sprite = this.sprites.find(s => s.id === this.selectedSpriteId);
        
        if (sprite) {
            document.getElementById('spriteName').value = sprite.name;
            document.getElementById('spriteX').value = sprite.x;
            document.getElementById('spriteY').value = sprite.y;
            document.getElementById('spriteWidth').value = sprite.width;
            document.getElementById('spriteHeight').value = sprite.height;
            
            document.getElementById('selectedSprite').textContent = sprite.name;
            document.getElementById('spritePosition').textContent = `${sprite.x}, ${sprite.y}`;
            document.getElementById('spriteSize').textContent = `${sprite.width}×${sprite.height}`;
        } else {
            document.getElementById('selectedSprite').textContent = 'None';
            document.getElementById('spritePosition').textContent = '-';
            document.getElementById('spriteSize').textContent = '-';
        }
    }
    
    updateSelectedSprite() {
        const sprite = this.sprites.find(s => s.id === this.selectedSpriteId);
        if (!sprite) return;
        
        sprite.x = parseInt(document.getElementById('spriteX').value) || 0;
        sprite.y = parseInt(document.getElementById('spriteY').value) || 0;
        sprite.width = parseInt(document.getElementById('spriteWidth').value) || 1;
        sprite.height = parseInt(document.getElementById('spriteHeight').value) || 1;
        
        this.updateSpriteList();
        this.drawCanvas();
    }
    
    applyProperties() {
        const sprite = this.sprites.find(s => s.id === this.selectedSpriteId);
        if (!sprite) return;
        
        sprite.name = document.getElementById('spriteName').value || `Sprite ${sprite.id + 1}`;
        this.updateSelectedSprite();
        this.updateSpritePreview();
        this.setStatus('Properties applied');
    }
    
    async updateSpritePreview() {
        const sprite = this.sprites.find(s => s.id === this.selectedSpriteId);
        const previewContainer = document.getElementById('spritePreview');
        
        if (!sprite || !this.spriteImage) {
            previewContainer.innerHTML = `<div class="preview-placeholder">${this.t('selectSprite')}</div>`;
            return;
        }
        
        // Create canvas for sprite extraction
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = sprite.width;
        canvas.height = sprite.height;
        
        // Draw sprite
        ctx.drawImage(this.spriteImage, 
            sprite.x, sprite.y, sprite.width, sprite.height,
            0, 0, sprite.width, sprite.height
        );
        
        // Create image element
        const img = document.createElement('img');
        img.src = canvas.toDataURL();
        img.alt = sprite.name;
        
        previewContainer.innerHTML = '';
        previewContainer.appendChild(img);
    }
    
    // Tool methods
    async autoDetectSprites() {
        await this.analyzeSprites(true);
    }
    
    setManualMode() {
        this.setStatus('Manual mode activated - Click to position sprites');
    }
    
    setGridMode() {
        const cols = parseInt(document.getElementById('gridCols').value) || 3;
        const rows = parseInt(document.getElementById('gridRows').value) || 3;
        
        if (!this.spriteImage) {
            this.setStatus('Please load a sprite sheet first', 'error');
            return;
        }
        
        const spriteWidth = Math.floor(this.spriteImage.width / cols);
        const spriteHeight = Math.floor(this.spriteImage.height / rows);
        
        this.sprites = [];
        for (let i = 0; i < rows * cols; i++) {
            const row = Math.floor(i / cols);
            const col = i % cols;
            
            this.sprites.push({
                id: i,
                x: col * spriteWidth,
                y: row * spriteHeight,
                width: spriteWidth,
                height: spriteHeight,
                name: `Sprite ${i + 1}`
            });
        }
        
        this.updateSpriteList();
        this.drawCanvas();
        this.setStatus(`Generated ${cols}×${rows} grid layout`);
    }
    
    resetLayout() {
        this.sprites = [];
        this.selectedSpriteId = null;
        this.updateSpriteList();
        this.updatePropertiesPanel();
        this.drawCanvas();
        this.setStatus('Layout reset');
    }
    
    // Zoom methods
    zoomIn() {
        this.zoom = Math.min(this.zoom * 1.2, 5);
        this.updateZoom();
    }
    
    zoomOut() {
        this.zoom = Math.max(this.zoom / 1.2, 0.1);
        this.updateZoom();
    }
    
    zoomFit() {
        if (!this.spriteImage) return;
        
        const container = document.getElementById('canvasContainer');
        const containerWidth = container.clientWidth - 40;
        const containerHeight = container.clientHeight - 40;
        
        const scaleX = containerWidth / this.spriteImage.width;
        const scaleY = containerHeight / this.spriteImage.height;
        
        this.zoom = Math.min(scaleX, scaleY, 1);
        this.updateZoom();
    }
    
    updateZoom() {
        this.canvas.style.transform = `scale(${this.zoom})`;
        document.getElementById('zoomLevel').textContent = `${Math.round(this.zoom * 100)}%`;
    }
    
    // Toggle methods
    toggleGrid(e) {
        e.target.classList.toggle('active');
        this.drawCanvas();
    }
    
    toggleLabels(e) {
        e.target.classList.toggle('active');
        this.drawCanvas();
    }
    
    toggleBounds(e) {
        e.target.classList.toggle('active');
        this.drawCanvas();
    }
    
    // Export methods
    async exportConfig(format) {
        if (this.sprites.length === 0) {
            this.setStatus(this.t('noSprites'), 'error');
            return;
        }

        try {
            const response = await fetch('/api/export_config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    format: format,
                    sprites: this.sprites
                })
            });

            const result = await response.json();
            if (!result.success) {
                throw new Error(result.error);
            }

            document.getElementById('exportContent').value =
                typeof result.config === 'string' ? result.config : JSON.stringify(result.config, null, 2);

            this.showModal(document.getElementById('exportModal'));
            this.setStatus(this.t('coordinatesGenerated'));

        } catch (error) {
            this.setStatus(`Export error: ${error.message}`, 'error');
        }
    }
    
    async generateVerification() {
        if (this.sprites.length === 0) {
            this.setStatus('No sprites to verify', 'error');
            return;
        }
        
        this.showLoading('Generating verification image...');
        
        try {
            const response = await fetch('/api/generate_verification', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({})
            });
            
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.error);
            }
            
            document.getElementById('verificationImage').src = result.verification_image;
            this.showModal(document.getElementById('verificationModal'));
            
        } catch (error) {
            this.setStatus(`Verification error: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    // Backup methods
    async listBackups() {
        try {
            const response = await fetch('/api/backup/list');
            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.error);
            }
            
            const select = document.getElementById('backupSelect');
            select.innerHTML = '<option value="">Select backup...</option>';
            
            result.backups.forEach(backup => {
                const option = document.createElement('option');
                option.value = backup;
                option.textContent = backup;
                select.appendChild(option);
            });
            
            select.style.display = 'block';
            document.getElementById('restoreBackup').style.display = 'block';
            
        } catch (error) {
            this.setStatus(`Backup list error: ${error.message}`, 'error');
        }
    }
    
    async restoreBackup() {
        const backupName = document.getElementById('backupSelect').value;
        if (!backupName) {
            this.setStatus('Please select a backup', 'error');
            return;
        }
        
        try {
            const response = await fetch('/api/backup/restore', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ backup_name: backupName })
            });
            
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.error);
            }
            
            this.setStatus('Backup restored successfully');
            
        } catch (error) {
            this.setStatus(`Restore error: ${error.message}`, 'error');
        }
    }
    
    // Utility methods
    copyToClipboard() {
        const content = document.getElementById('exportContent');
        content.select();
        document.execCommand('copy');
        this.setStatus('Copied to clipboard');
    }
    
    showModal(modal) {
        modal.classList.add('show');
    }
    
    hideModal(modal) {
        modal.classList.remove('show');
    }
    
    showLoadProjectModal() {
        // 检测当前项目文件
        this.detectProjectFiles();
        this.showModal(document.getElementById('loadProjectModal'));
    }

    async detectProjectFiles() {
        const projectPath = document.getElementById('projectPath').value || '../';

        try {
            // 尝试初始化项目以检测文件
            const response = await fetch('/api/detect_project', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ project_path: projectPath })
            });

            const result = await response.json();

            if (result.success) {
                this.showDetectedFiles(result.files);

                // 自动填充精灵图路径
                if (result.sprite_sheets && result.sprite_sheets.length > 0) {
                    document.getElementById('spriteSheetPath').value = result.sprite_sheets[0];
                }
            } else {
                this.hideDetectedFiles();
            }
        } catch (error) {
            console.log('项目检测失败:', error);
            this.hideDetectedFiles();
        }
    }

    showDetectedFiles(files) {
        const infoDiv = document.getElementById('projectInfo');
        const filesList = document.getElementById('detectedFilesList');

        filesList.innerHTML = '';

        // 显示检测到的文件类型
        const fileTypes = {
            'Game Engine': files.engine || 'Unknown',
            'Runtime Files': files.runtime_files || [],
            'Sprite Sheets': files.sprite_sheets || [],
            'Audio Files': files.audio_files || [],
            'Config Files': files.config_files || []
        };

        Object.entries(fileTypes).forEach(([type, items]) => {
            const li = document.createElement('li');
            if (Array.isArray(items)) {
                li.innerHTML = `<strong>${type}:</strong> ${items.length > 0 ? items.slice(0, 3).join(', ') + (items.length > 3 ? '...' : '') : 'None'}`;
            } else {
                li.innerHTML = `<strong>${type}:</strong> ${items}`;
            }
            filesList.appendChild(li);
        });

        // 自动设置引擎选择器（如果用户没有手动选择）
        const engineSelect = document.getElementById('engineOverride');
        if (engineSelect.value === '') {
            // 根据检测结果设置默认值
            const engineMap = {
                'Construct 2/3': 'construct2',
                'Godot': 'godot',
                'Unity': 'unity',
                'Web Game': 'web',
                'Generic Game': 'generic'
            };
            const detectedEngine = engineMap[files.engine] || '';
            if (detectedEngine) {
                engineSelect.value = detectedEngine;
            }
        }

        infoDiv.style.display = 'block';
    }

    hideDetectedFiles() {
        document.getElementById('projectInfo').style.display = 'none';
    }

    browseProjectPath() {
        // 创建文件选择器（用于选择文件夹）
        const input = document.createElement('input');
        input.type = 'file';
        input.webkitdirectory = true; // 允许选择文件夹
        input.multiple = true;

        input.onchange = (e) => {
            const files = e.target.files;
            if (files.length > 0) {
                // 获取第一个文件的路径，提取文件夹路径
                const firstFile = files[0];
                const pathParts = firstFile.webkitRelativePath.split('/');
                const folderPath = pathParts[0]; // 获取根文件夹名

                document.getElementById('projectPath').value = './' + folderPath;
                this.detectProjectFiles();
            }
        };

        input.click();
    }

    browseSpriteSheet() {
        // 创建文件选择器（用于选择图片文件）
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';

        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                // 这里我们只能获取文件名，实际路径需要用户手动调整
                document.getElementById('spriteSheetPath').value = 'images/' + file.name;
            }
        };

        input.click();
    }
    
    showLoading(message = 'Loading...') {
        document.querySelector('.loading-text').textContent = message;
        document.getElementById('loadingOverlay').classList.add('show');
    }
    
    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('show');
    }
    
    setStatus(message, type = 'info') {
        document.getElementById('statusMessage').textContent = message;
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
    
    updateUI() {
        this.updateSpriteList();
        this.updatePropertiesPanel();
        this.drawCanvas();
    }
    
    // Mouse handling for dragging
    handleMouseDown(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = (e.clientX - rect.left) / this.zoom;
        const y = (e.clientY - rect.top) / this.zoom;
        
        // Check if clicking on a sprite
        for (let sprite of this.sprites) {
            if (x >= sprite.x && x <= sprite.x + sprite.width &&
                y >= sprite.y && y <= sprite.y + sprite.height) {
                this.isDragging = true;
                this.selectSprite(sprite.id);
                this.dragOffset = {
                    x: x - sprite.x,
                    y: y - sprite.y
                };
                break;
            }
        }
    }
    
    handleMouseMove(e) {
        if (!this.isDragging || this.selectedSpriteId === null) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const x = (e.clientX - rect.left) / this.zoom;
        const y = (e.clientY - rect.top) / this.zoom;
        
        const sprite = this.sprites.find(s => s.id === this.selectedSpriteId);
        if (sprite) {
            sprite.x = Math.round(x - this.dragOffset.x);
            sprite.y = Math.round(y - this.dragOffset.y);
            
            this.updatePropertiesPanel();
            this.drawCanvas();
        }
    }
    
    handleMouseUp(e) {
        if (this.isDragging) {
            this.isDragging = false;
            this.updateSpriteList();
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.spriteApp = new SpriteToolkitApp();
});
