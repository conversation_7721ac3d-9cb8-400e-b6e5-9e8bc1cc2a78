#!/usr/bin/env python3
"""
🚀 Sprite Toolkit Launcher
一键启动统一的精灵图工具包
"""

import os
import sys
import subprocess
import webbrowser
import time
import signal

def check_dependencies():
    """检查并安装依赖"""
    required_packages = [
        'Pillow',
        'numpy',
        'flask',
        'flask-cors'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'Pillow':
                import PIL
            elif package == 'numpy':
                import numpy
            elif package == 'flask':
                import flask
            elif package == 'flask-cors':
                import flask_cors
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print()
        
        install = input("是否自动安装缺少的依赖? (y/n): ").strip().lower()
        if install == 'y':
            try:
                for package in missing_packages:
                    print(f"📦 安装 {package}...")
                    subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print("✅ 所有依赖已安装完成!")
                return True
            except subprocess.CalledProcessError as e:
                print(f"❌ 安装失败: {e}")
                return False
        else:
            print("❌ 请手动安装依赖后重试")
            return False
    
    print("✅ 所有依赖检查通过")
    return True

def find_project_path():
    """查找项目路径"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 检查上级目录是否包含游戏文件
    parent_dir = os.path.dirname(current_dir)
    
    if os.path.exists(os.path.join(parent_dir, 'index.html')) and \
       os.path.exists(os.path.join(parent_dir, 'c2runtime.js')):
        return parent_dir
    
    # 检查当前目录
    if os.path.exists(os.path.join(current_dir, 'index.html')):
        return current_dir
    
    return parent_dir

def start_server(project_path):
    """启动服务器"""
    try:
        print("🚀 启动 Sprite Toolkit 服务器...")
        print(f"📁 项目路径: {project_path}")
        print("🌐 服务器地址: http://localhost:5000")
        print("=" * 50)
        
        # 启动服务器
        env = os.environ.copy()
        env['PYTHONPATH'] = os.path.dirname(os.path.abspath(__file__))
        
        process = subprocess.Popen([
            sys.executable, 'server.py', project_path
        ], cwd=os.path.dirname(os.path.abspath(__file__)), env=env)
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        time.sleep(3)
        
        # 打开浏览器
        print("🌐 打开浏览器...")
        webbrowser.open('http://localhost:5000')
        
        print("✅ Sprite Toolkit 已启动!")
        print("💡 按 Ctrl+C 停止服务器")
        print()
        
        # 等待用户中断
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务器...")
            process.terminate()
            process.wait()
            print("✅ 服务器已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        return False

def show_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("🎨 Sprite Toolkit - 统一精灵图工具包")
    print("="*60)
    print("1. 🚀 启动 Web 工具包 (推荐)")
    print("2. 🔧 使用传统工具")
    print("3. 📚 查看帮助文档")
    print("4. 🧹 清理临时文件")
    print("5. ❌ 退出")
    print("="*60)

def show_traditional_tools():
    """显示传统工具菜单"""
    print("\n📋 传统工具列表:")
    print("1. 可视化调整工具 (sprite_adjuster_fixed.html)")
    print("2. 精灵诊断工具 (diagnose_sprites.py)")
    print("3. 自动修复工具 (auto_fix_sprites.py)")
    print("4. 坐标应用工具 (apply_coordinates.py)")
    print("5. 返回主菜单")

def run_traditional_tool(choice, project_path):
    """运行传统工具"""
    tools_dir = os.path.join(project_path, 'sprite-tools')
    
    if choice == '1':
        # 启动HTTP服务器并打开调整工具
        try:
            print("🌐 启动HTTP服务器...")
            process = subprocess.Popen([
                sys.executable, '-m', 'http.server', '8000'
            ], cwd=project_path)
            
            time.sleep(2)
            webbrowser.open('http://localhost:8000/sprite-tools/sprite_adjuster_fixed.html')
            
            print("✅ 调整工具已打开")
            print("💡 按 Ctrl+C 停止服务器")
            
            try:
                process.wait()
            except KeyboardInterrupt:
                process.terminate()
                print("\n✅ 服务器已停止")
                
        except Exception as e:
            print(f"❌ 启动失败: {e}")
    
    elif choice == '2':
        subprocess.run([sys.executable, os.path.join(tools_dir, 'diagnose_sprites.py')])
    
    elif choice == '3':
        subprocess.run([sys.executable, os.path.join(tools_dir, 'auto_fix_sprites.py')])
    
    elif choice == '4':
        subprocess.run([sys.executable, os.path.join(tools_dir, 'apply_coordinates.py')])

def show_help():
    """显示帮助信息"""
    help_text = """
📚 Sprite Toolkit 帮助文档

🎯 主要功能:
• 可视化精灵图编辑
• 自动精灵检测
• 多游戏引擎支持
• 实时预览和验证
• 备份和恢复功能

🛠️ Web工具包特性:
• 现代化Web界面
• 拖拽调整精灵位置
• 实时预览效果
• 多种导出格式
• 智能网格对齐

📋 使用流程:
1. 启动Web工具包
2. 加载项目和精灵图
3. 调整精灵位置和尺寸
4. 预览和验证效果
5. 保存更改到游戏

💡 技巧:
• 使用自动检测快速开始
• 启用网格对齐精确定位
• 使用验证图检查效果
• 定期备份重要文件

🔗 更多信息请查看 README.md
"""
    print(help_text)

def cleanup_temp_files(project_path):
    """清理临时文件"""
    temp_patterns = [
        '*.pyc',
        '__pycache__',
        '.DS_Store',
        'Thumbs.db'
    ]
    
    print("🧹 清理临时文件...")
    
    import glob
    cleaned = 0
    
    for pattern in temp_patterns:
        files = glob.glob(os.path.join(project_path, '**', pattern), recursive=True)
        for file in files:
            try:
                if os.path.isfile(file):
                    os.remove(file)
                elif os.path.isdir(file):
                    import shutil
                    shutil.rmtree(file)
                cleaned += 1
                print(f"🗑️ 已删除: {os.path.relpath(file, project_path)}")
            except Exception as e:
                print(f"❌ 删除失败 {file}: {e}")
    
    print(f"✅ 清理完成，删除了 {cleaned} 个文件/文件夹")

def main():
    """主函数"""
    print("🎮 Sprite Toolkit Launcher")
    print("统一精灵图工具包启动器")
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 查找项目路径
    project_path = find_project_path()
    print(f"📁 检测到项目路径: {project_path}")
    
    while True:
        show_menu()
        choice = input("\n请选择操作 (1-5): ").strip()
        
        if choice == '1':
            if start_server(project_path):
                break
        
        elif choice == '2':
            while True:
                show_traditional_tools()
                tool_choice = input("\n请选择工具 (1-5): ").strip()
                
                if tool_choice in ['1', '2', '3', '4']:
                    run_traditional_tool(tool_choice, project_path)
                elif tool_choice == '5':
                    break
                else:
                    print("❌ 无效选择")
        
        elif choice == '3':
            show_help()
            input("\n按回车键继续...")
        
        elif choice == '4':
            cleanup_temp_files(project_path)
            input("\n按回车键继续...")
        
        elif choice == '5':
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重试")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，再见!")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        input("按回车键退出...")
