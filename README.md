# 鱼类世界 - Match 3 游戏

一个基于 HTML5 Canvas 和 Construct 2 引擎开发的三消类游戏。

## 🎮 游戏特色

- **经典三消玩法**：匹配三个或更多相同的鱼类来消除它们
- **精美图形**：高质量的鱼类和海洋主题图像
- **音效支持**：丰富的游戏音效和背景音乐
- **移动端优化**：完美支持触摸操作和移动设备
- **离线游戏**：支持离线缓存，无网络也能畅玩

## 🚀 最新优化 (2024)

### 性能优化
- ✅ **解决 jQuery 版本冲突**：移除重复的 jQuery 1.8.3，统一使用 jQuery 2.0.0
- ✅ **Canvas 渲染优化**：启用硬件加速，优化图像渲染性能
- ✅ **FPS 监控**：实时监控游戏帧率，确保流畅体验
- ✅ **内存管理**：优化资源加载和清理机制
- ✅ **事件处理优化**：使用防抖技术优化窗口大小调整

### 移动端体验
- ✅ **响应式设计**：完美适配各种屏幕尺寸
- ✅ **触摸优化**：改进触摸事件处理，防止意外滚动
- ✅ **全屏支持**：支持 iOS 和 Android 全屏模式
- ✅ **方向锁定**：智能处理设备旋转

### 离线功能
- ✅ **应用缓存**：创建完整的离线缓存清单
- ✅ **资源预加载**：优化资源加载策略
- ✅ **PWA 支持**：支持添加到主屏幕

### 错误处理
- ✅ **全局错误捕获**：捕获并记录运行时错误
- ✅ **优雅降级**：在出现问题时提供备用方案
- ✅ **调试信息**：开发模式下提供详细的调试信息

## 📁 项目结构

```
鱼类世界-MatCH3/
├── index.html              # 主游戏页面
├── c2runtime.js           # Construct 2 运行时
├── jquery-2.0.0.min.js    # jQuery 库
├── jquery.equalheights.js # jQuery 插件
├── main.css               # 主样式文件
├── style.css              # 优化后的样式
├── page_achievements.css  # 成就页面样式
├── page_leaderboards.css  # 排行榜样式
├── app.manifest           # 应用清单
├── offline.appcache       # 离线缓存清单
├── images/                # 游戏图像资源
│   ├── fish-sheet0.png    # 鱼类精灵图
│   ├── background-sheet0.png # 背景图
│   └── ...                # 其他游戏资源
├── media/                 # 音频资源
│   ├── gamemusic.ogg      # 背景音乐
│   ├── match.ogg          # 匹配音效
│   └── ...                # 其他音效文件
└── README.md              # 项目说明文档
```

## 🛠️ 技术栈

- **游戏引擎**：Construct 2
- **渲染技术**：HTML5 Canvas
- **前端框架**：jQuery 2.0.0
- **样式技术**：CSS3 + 响应式设计
- **音频支持**：Web Audio API
- **离线技术**：Application Cache

## 🎯 系统要求

### 桌面端
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

### 移动端
- iOS Safari 11+
- Android Chrome 60+
- Android WebView 60+

## 🚀 快速开始

1. **下载项目**
   ```bash
   # 下载并解压项目文件
   ```

2. **本地运行**
   ```bash
   # 使用本地服务器运行（推荐）
   python -m http.server 8000
   # 或使用 Node.js
   npx serve .
   ```

3. **访问游戏**
   ```
   打开浏览器访问：http://localhost:8000
   ```

## 📱 移动端安装

### iOS 设备
1. 使用 Safari 浏览器打开游戏
2. 点击分享按钮
3. 选择"添加到主屏幕"
4. 确认添加

### Android 设备
1. 使用 Chrome 浏览器打开游戏
2. 点击菜单按钮
3. 选择"添加到主屏幕"
4. 确认添加

## 🎮 游戏玩法

1. **基础操作**
   - 点击或触摸相邻的鱼类进行交换
   - 形成三个或更多相同鱼类的连线来消除
   - 消除更多鱼类获得更高分数

2. **特殊道具**
   - 炸弹鱼：消除周围的鱼类
   - 彩虹鱼：可以匹配任意颜色
   - 闪电鱼：消除整行或整列

3. **游戏目标**
   - 在限定时间内达到目标分数
   - 完成各种挑战任务
   - 解锁新的关卡和成就

## 🔧 开发者信息

### 性能监控
游戏内置了性能监控功能，可以在浏览器控制台查看：
- FPS（帧率）
- 内存使用情况
- 加载时间

### 调试模式
在控制台输入以下命令启用调试模式：
```javascript
// 显示 FPS
console.log('当前 FPS:', gameStats.fps);

// 查看游戏状态
console.log('游戏是否暂停:', isGameSuspended);
```

## 📄 更新日志

### v1.1.0 (2024-01-01)
- 🔧 修复 jQuery 版本冲突问题
- ⚡ 大幅提升 Canvas 渲染性能
- 📱 优化移动端触摸体验
- 💾 添加完整的离线缓存支持
- 🛡️ 增强错误处理和调试功能
- 🎨 改进 UI 样式和响应式设计

### v1.0.0 (原始版本)
- 🎮 基础三消游戏功能
- 🎵 音效和背景音乐
- 🏆 成就和排行榜系统

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进游戏！

## 📞 技术支持

如果遇到问题，请检查：
1. 浏览器是否支持 HTML5 Canvas
2. 是否启用了 JavaScript
3. 网络连接是否正常（首次加载）

## 📜 许可证

本项目仅供学习和研究使用。

---

**享受游戏乐趣！** 🐠🎮✨