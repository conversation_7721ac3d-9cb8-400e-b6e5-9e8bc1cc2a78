<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>🎨 精灵图调整工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .canvas-container {
            position: relative;
            display: inline-block;
            border: 2px solid #333;
            margin: 20px 0;
        }
        
        #spriteCanvas {
            display: block;
            cursor: crosshair;
        }
        
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
            align-items: center;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 5px;
            background: #f8f8f8;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        
        input[type="number"] {
            width: 60px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        
        button {
            padding: 8px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button.danger {
            background: #dc3545;
        }
        
        button.danger:hover {
            background: #c82333;
        }
        
        button.success {
            background: #28a745;
        }
        
        button.success:hover {
            background: #218838;
        }
        
        .sprite-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .sprite-item {
            background: #f8f8f8;
            border: 2px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
        }
        
        .sprite-item.selected {
            border-color: #007bff;
            background: #e7f3ff;
        }
        
        .sprite-preview {
            width: 100px;
            height: 100px;
            background: #fff;
            border: 1px solid #ccc;
            margin: 5px auto;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .coordinates-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .instructions {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 精灵图调整工具</h1>
        
        <div class="instructions">
            <h3>📋 使用说明：</h3>
            <ol>
                <li><strong>点击画布</strong> 设置当前精灵的位置</li>
                <li><strong>调整尺寸</strong> 使用下方的宽度和高度控件</li>
                <li><strong>切换精灵</strong> 点击下方的精灵列表选择不同精灵</li>
                <li><strong>预览效果</strong> 实时查看每个精灵的提取效果</li>
                <li><strong>导出坐标</strong> 完成后点击"生成坐标代码"</li>
            </ol>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>当前精灵:</label>
                <select id="currentSprite">
                    <option value="0">精灵 1</option>
                    <option value="1">精灵 2</option>
                    <option value="2">精灵 3</option>
                    <option value="3">精灵 4</option>
                    <option value="4">精灵 5</option>
                    <option value="5">精灵 6</option>
                    <option value="6">精灵 7</option>
                    <option value="7">精灵 8</option>
                    <option value="8">精灵 9</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>X:</label>
                <input type="number" id="spriteX" value="0" min="0" max="1024">
            </div>
            
            <div class="control-group">
                <label>Y:</label>
                <input type="number" id="spriteY" value="0" min="0" max="1024">
            </div>
            
            <div class="control-group">
                <label>宽度:</label>
                <input type="number" id="spriteWidth" value="258" min="50" max="500">
            </div>
            
            <div class="control-group">
                <label>高度:</label>
                <input type="number" id="spriteHeight" value="311" min="50" max="500">
            </div>
            
            <button onclick="updateCurrentSprite()">更新当前精灵</button>
            <button onclick="resetGrid()">重置网格</button>
            <button class="success" onclick="generateCoordinates()">生成坐标代码</button>
        </div>
        
        <div class="canvas-container">
            <canvas id="spriteCanvas" width="1024" height="1024"></canvas>
        </div>
        
        <div class="sprite-list" id="spriteList">
            <!-- 精灵列表将在这里动态生成 -->
        </div>
        
        <div id="status"></div>
        
        <div class="coordinates-output" id="coordinatesOutput">
            坐标将在这里显示...
        </div>
    </div>

    <script>
        // 精灵数据
        let sprites = [
            {x: 0, y: 0, width: 258, height: 311},
            {x: 258, y: 0, width: 258, height: 311},
            {x: 516, y: 0, width: 258, height: 311},
            {x: 0, y: 311, width: 258, height: 311},
            {x: 258, y: 311, width: 258, height: 311},
            {x: 516, y: 311, width: 258, height: 311},
            {x: 0, y: 622, width: 258, height: 311},
            {x: 258, y: 622, width: 258, height: 311},
            {x: 516, y: 622, width: 258, height: 311}
        ];
        
        let currentSpriteIndex = 0;
        let canvas, ctx;
        let spriteImage = null;
        
        // 初始化
        window.onload = function() {
            canvas = document.getElementById('spriteCanvas');
            ctx = canvas.getContext('2d');
            
            // 加载精灵图
            loadSpriteImage();
            
            // 设置事件监听器
            setupEventListeners();
            
            // 初始化界面
            updateUI();
            drawCanvas();
        };
        
        function loadSpriteImage() {
            spriteImage = new Image();
            spriteImage.onload = function() {
                drawCanvas();
                showStatus('✅ 精灵图加载成功', 'success');
            };
            spriteImage.onerror = function() {
                showStatus('❌ 精灵图加载失败', 'error');
            };
            spriteImage.src = 'images/fish-sheet0.png';
        }
        
        function setupEventListeners() {
            // 画布点击事件
            canvas.addEventListener('click', function(e) {
                const rect = canvas.getBoundingClientRect();
                const x = Math.round(e.clientX - rect.left);
                const y = Math.round(e.clientY - rect.top);
                
                sprites[currentSpriteIndex].x = x;
                sprites[currentSpriteIndex].y = y;
                
                updateUI();
                drawCanvas();
            });
            
            // 输入框变化事件
            ['spriteX', 'spriteY', 'spriteWidth', 'spriteHeight'].forEach(id => {
                document.getElementById(id).addEventListener('input', updateCurrentSprite);
            });
            
            // 精灵选择变化
            document.getElementById('currentSprite').addEventListener('change', function() {
                currentSpriteIndex = parseInt(this.value);
                updateUI();
                drawCanvas();
            });
        }
        
        function drawCanvas() {
            if (!spriteImage) return;
            
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制精灵图
            ctx.drawImage(spriteImage, 0, 0);
            
            // 绘制所有精灵边框
            sprites.forEach((sprite, index) => {
                ctx.strokeStyle = index === currentSpriteIndex ? '#ff0000' : '#00ff00';
                ctx.lineWidth = index === currentSpriteIndex ? 3 : 2;
                ctx.strokeRect(sprite.x, sprite.y, sprite.width, sprite.height);
                
                // 绘制精灵编号
                ctx.fillStyle = index === currentSpriteIndex ? '#ff0000' : '#00ff00';
                ctx.font = '16px Arial';
                ctx.fillText(`${index + 1}`, sprite.x + 5, sprite.y + 20);
            });
        }
        
        function updateUI() {
            const sprite = sprites[currentSpriteIndex];
            document.getElementById('spriteX').value = sprite.x;
            document.getElementById('spriteY').value = sprite.y;
            document.getElementById('spriteWidth').value = sprite.width;
            document.getElementById('spriteHeight').value = sprite.height;
            document.getElementById('currentSprite').value = currentSpriteIndex;
            
            updateSpriteList();
        }
        
        function updateCurrentSprite() {
            const sprite = sprites[currentSpriteIndex];
            sprite.x = parseInt(document.getElementById('spriteX').value) || 0;
            sprite.y = parseInt(document.getElementById('spriteY').value) || 0;
            sprite.width = parseInt(document.getElementById('spriteWidth').value) || 258;
            sprite.height = parseInt(document.getElementById('spriteHeight').value) || 311;
            
            drawCanvas();
            updateSpriteList();
        }
        
        function updateSpriteList() {
            const listContainer = document.getElementById('spriteList');
            listContainer.innerHTML = '';
            
            sprites.forEach((sprite, index) => {
                const item = document.createElement('div');
                item.className = `sprite-item ${index === currentSpriteIndex ? 'selected' : ''}`;
                item.onclick = () => {
                    currentSpriteIndex = index;
                    updateUI();
                    drawCanvas();
                };
                
                item.innerHTML = `
                    <h4>精灵 ${index + 1}</h4>
                    <div class="sprite-preview" id="preview-${index}"></div>
                    <div>X: ${sprite.x}, Y: ${sprite.y}</div>
                    <div>尺寸: ${sprite.width}×${sprite.height}</div>
                `;
                
                listContainer.appendChild(item);
                
                // 更新预览
                updateSpritePreview(index);
            });
        }
        
        function updateSpritePreview(index) {
            if (!spriteImage) return;
            
            const sprite = sprites[index];
            const preview = document.getElementById(`preview-${index}`);
            
            // 创建临时画布来提取精灵
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = sprite.width;
            tempCanvas.height = sprite.height;
            const tempCtx = tempCanvas.getContext('2d');
            
            tempCtx.drawImage(spriteImage, 
                sprite.x, sprite.y, sprite.width, sprite.height,
                0, 0, sprite.width, sprite.height
            );
            
            preview.style.backgroundImage = `url(${tempCanvas.toDataURL()})`;
        }
        
        function resetGrid() {
            // 重置为3x3网格
            const gridWidth = 258;
            const gridHeight = 311;
            
            for (let i = 0; i < 9; i++) {
                const row = Math.floor(i / 3);
                const col = i % 3;
                sprites[i] = {
                    x: col * gridWidth,
                    y: row * gridHeight,
                    width: gridWidth,
                    height: gridHeight
                };
            }
            
            updateUI();
            drawCanvas();
            showStatus('✅ 网格已重置为3×3布局', 'success');
        }
        
        function generateCoordinates() {
            let output = '// 生成的精灵坐标\n';
            output += 'const spriteCoordinates = [\n';
            
            sprites.forEach((sprite, index) => {
                output += `    {x: ${sprite.x}, y: ${sprite.y}, width: ${sprite.width}, height: ${sprite.height}}, // 精灵 ${index + 1}\n`;
            });
            
            output += '];\n\n';
            
            // 生成Python更新脚本
            output += '# Python更新脚本坐标\n';
            output += 'coordinates = [\n';
            sprites.forEach((sprite, index) => {
                output += `    (${sprite.x}, ${sprite.y}, ${sprite.width}, ${sprite.height}),  # 精灵 ${index + 1}\n`;
            });
            output += ']\n';
            
            document.getElementById('coordinatesOutput').textContent = output;
            showStatus('✅ 坐标代码已生成', 'success');
        }
        
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            
            setTimeout(() => {
                status.textContent = '';
                status.className = 'status';
            }, 3000);
        }
    </script>
</body>
</html>
