<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sprite Adjuster Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .canvas-container {
            position: relative;
            display: inline-block;
            border: 2px solid #333;
            margin: 20px 0;
        }
        
        #spriteCanvas {
            display: block;
            cursor: crosshair;
        }
        
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
            align-items: center;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 5px;
            background: #f8f8f8;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        
        input[type="number"] {
            width: 60px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        
        button {
            padding: 8px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button.success {
            background: #28a745;
        }
        
        button.success:hover {
            background: #218838;
        }
        
        .sprite-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .sprite-item {
            background: #f8f8f8;
            border: 2px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
            cursor: pointer;
        }
        
        .sprite-item.selected {
            border-color: #007bff;
            background: #e7f3ff;
        }
        
        .coordinates-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .instructions {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Sprite Position Adjuster</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li><strong>Click canvas</strong> to set sprite position</li>
                <li><strong>Adjust size</strong> using width and height controls</li>
                <li><strong>Switch sprites</strong> by clicking sprite cards below</li>
                <li><strong>Generate coordinates</strong> when finished</li>
            </ol>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>Current Sprite:</label>
                <select id="currentSprite">
                    <option value="0">Sprite 1</option>
                    <option value="1">Sprite 2</option>
                    <option value="2">Sprite 3</option>
                    <option value="3">Sprite 4</option>
                    <option value="4">Sprite 5</option>
                    <option value="5">Sprite 6</option>
                    <option value="6">Sprite 7</option>
                    <option value="7">Sprite 8</option>
                    <option value="8">Sprite 9</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>X:</label>
                <input type="number" id="spriteX" value="0" min="0" max="1024">
            </div>
            
            <div class="control-group">
                <label>Y:</label>
                <input type="number" id="spriteY" value="0" min="0" max="1024">
            </div>
            
            <div class="control-group">
                <label>Width:</label>
                <input type="number" id="spriteWidth" value="258" min="50" max="500">
            </div>
            
            <div class="control-group">
                <label>Height:</label>
                <input type="number" id="spriteHeight" value="311" min="50" max="500">
            </div>
            
            <button onclick="updateCurrentSprite()">Update Sprite</button>
            <button onclick="resetGrid()">Reset Grid</button>
            <button class="success" onclick="generateCoordinates()">Generate Coordinates</button>
        </div>
        
        <div class="canvas-container">
            <canvas id="spriteCanvas" width="1024" height="1024"></canvas>
        </div>
        
        <div class="sprite-list" id="spriteList">
            <!-- Sprite list will be generated here -->
        </div>
        
        <div id="status"></div>
        
        <div class="coordinates-output" id="coordinatesOutput">
            Coordinates will appear here...
        </div>
    </div>

    <script>
        // Sprite data
        let sprites = [
            {x: 0, y: 0, width: 258, height: 311},
            {x: 258, y: 0, width: 258, height: 311},
            {x: 516, y: 0, width: 258, height: 311},
            {x: 0, y: 311, width: 258, height: 311},
            {x: 258, y: 311, width: 258, height: 311},
            {x: 516, y: 311, width: 258, height: 311},
            {x: 0, y: 622, width: 258, height: 311},
            {x: 258, y: 622, width: 258, height: 311},
            {x: 516, y: 622, width: 258, height: 311}
        ];
        
        let currentSpriteIndex = 0;
        let canvas, ctx;
        let spriteImage = null;
        
        // Initialize
        window.onload = function() {
            canvas = document.getElementById('spriteCanvas');
            ctx = canvas.getContext('2d');
            
            // Load sprite image
            loadSpriteImage();
            
            // Setup event listeners
            setupEventListeners();
            
            // Initialize UI
            updateUI();
            drawCanvas();
        };
        
        function loadSpriteImage() {
            spriteImage = new Image();
            spriteImage.onload = function() {
                drawCanvas();
                showStatus('Sprite image loaded successfully', 'success');
            };
            spriteImage.onerror = function() {
                showStatus('Failed to load sprite image', 'error');
            };
            spriteImage.src = 'images/fish-sheet0.png';
        }
        
        function setupEventListeners() {
            // Canvas click event
            canvas.addEventListener('click', function(e) {
                const rect = canvas.getBoundingClientRect();
                const x = Math.round(e.clientX - rect.left);
                const y = Math.round(e.clientY - rect.top);
                
                sprites[currentSpriteIndex].x = x;
                sprites[currentSpriteIndex].y = y;
                
                updateUI();
                drawCanvas();
            });
            
            // Input change events
            ['spriteX', 'spriteY', 'spriteWidth', 'spriteHeight'].forEach(id => {
                document.getElementById(id).addEventListener('input', updateCurrentSprite);
            });
            
            // Sprite selection change
            document.getElementById('currentSprite').addEventListener('change', function() {
                currentSpriteIndex = parseInt(this.value);
                updateUI();
                drawCanvas();
            });
        }
        
        function drawCanvas() {
            if (!spriteImage) return;
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw sprite image
            ctx.drawImage(spriteImage, 0, 0);
            
            // Draw all sprite borders
            sprites.forEach((sprite, index) => {
                ctx.strokeStyle = index === currentSpriteIndex ? '#ff0000' : '#00ff00';
                ctx.lineWidth = index === currentSpriteIndex ? 3 : 2;
                ctx.strokeRect(sprite.x, sprite.y, sprite.width, sprite.height);
                
                // Draw sprite number
                ctx.fillStyle = index === currentSpriteIndex ? '#ff0000' : '#00ff00';
                ctx.font = '16px Arial';
                ctx.fillText(`${index + 1}`, sprite.x + 5, sprite.y + 20);
            });
        }
        
        function updateUI() {
            const sprite = sprites[currentSpriteIndex];
            document.getElementById('spriteX').value = sprite.x;
            document.getElementById('spriteY').value = sprite.y;
            document.getElementById('spriteWidth').value = sprite.width;
            document.getElementById('spriteHeight').value = sprite.height;
            document.getElementById('currentSprite').value = currentSpriteIndex;
            
            updateSpriteList();
        }
        
        function updateCurrentSprite() {
            const sprite = sprites[currentSpriteIndex];
            sprite.x = parseInt(document.getElementById('spriteX').value) || 0;
            sprite.y = parseInt(document.getElementById('spriteY').value) || 0;
            sprite.width = parseInt(document.getElementById('spriteWidth').value) || 258;
            sprite.height = parseInt(document.getElementById('spriteHeight').value) || 311;
            
            drawCanvas();
            updateSpriteList();
        }
        
        function updateSpriteList() {
            const listContainer = document.getElementById('spriteList');
            listContainer.innerHTML = '';
            
            sprites.forEach((sprite, index) => {
                const item = document.createElement('div');
                item.className = `sprite-item ${index === currentSpriteIndex ? 'selected' : ''}`;
                item.onclick = () => {
                    currentSpriteIndex = index;
                    updateUI();
                    drawCanvas();
                };
                
                item.innerHTML = `
                    <h4>Sprite ${index + 1}</h4>
                    <div>X: ${sprite.x}, Y: ${sprite.y}</div>
                    <div>Size: ${sprite.width}×${sprite.height}</div>
                `;
                
                listContainer.appendChild(item);
            });
        }
        
        function resetGrid() {
            // Reset to 3x3 grid
            const gridWidth = 258;
            const gridHeight = 311;
            
            for (let i = 0; i < 9; i++) {
                const row = Math.floor(i / 3);
                const col = i % 3;
                sprites[i] = {
                    x: col * gridWidth,
                    y: row * gridHeight,
                    width: gridWidth,
                    height: gridHeight
                };
            }
            
            updateUI();
            drawCanvas();
            showStatus('Grid reset to 3×3 layout', 'success');
        }
        
        function generateCoordinates() {
            let output = '# Generated sprite coordinates\n';
            output += 'coordinates = [\n';
            
            sprites.forEach((sprite, index) => {
                output += `    (${sprite.x}, ${sprite.y}, ${sprite.width}, ${sprite.height}),  # Sprite ${index + 1}\n`;
            });
            
            output += ']\n\n';
            output += '# Copy these coordinates to apply_coordinates.py';
            
            document.getElementById('coordinatesOutput').textContent = output;
            showStatus('Coordinates generated successfully', 'success');
        }
        
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            
            setTimeout(() => {
                status.textContent = '';
                status.className = 'status';
            }, 3000);
        }
    </script>
</body>
</html>
