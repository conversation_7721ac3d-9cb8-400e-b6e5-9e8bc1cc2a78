/* Fish World Match 3 - Enhanced Styles */
* {
	box-sizing: border-box;
	margin: 0;
	padding: 0;
}

/* Performance optimizations */
html {
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
	text-size-adjust: 100%;
}

body {
	font-family: Arial, sans-serif;
	background: #000;
	overflow: hidden;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-webkit-touch-callout: none;
	-webkit-tap-highlight-color: transparent;
}

/* Canvas optimizations */
canvas {
	display: block;
	image-rendering: -webkit-optimize-contrast;
	image-rendering: -moz-crisp-edges;
	image-rendering: pixelated;
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
	-webkit-transform: translateZ(0);
	transform: translateZ(0);
}

/* Game container */
#c2canvasdiv {
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
	z-index: 1;
}

#c2canvas {
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	max-width: 100vw;
	max-height: 100vh;
	object-fit: contain;
	z-index: 1;
}

/* Loading screen */
.loading {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: #000;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
}

.loading img {
	max-width: 200px;
	height: auto;
}

/* Mobile optimizations */
@media (max-width: 768px) {
	body {
		-webkit-overflow-scrolling: touch;
	}
	
	#c2canvas {
		width: 100vw !important;
		height: 100vh !important;
		object-fit: cover;
	}
}

/* Prevent zoom on mobile */
@media (max-width: 480px) {
	meta[name="viewport"] {
		content: "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no";
	}
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
	canvas {
		image-rendering: -webkit-optimize-contrast;
	}
}