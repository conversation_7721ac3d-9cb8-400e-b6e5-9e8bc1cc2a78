# 🎮 鱼类世界游戏 - 项目结构

## 📁 核心游戏文件
```
├── index.html              # 游戏主页面
├── c2runtime.js           # 游戏运行时引擎
├── offline.appcache       # 离线缓存配置
├── style.css              # 游戏样式
├── main.css               # 主要样式
└── app.manifest           # 应用清单
```

## 🎨 资源文件
```
├── images/                # 游戏图片资源
│   ├── fish-sheet0.png    # 主要精灵图（9个角色）
│   ├── fish-sheet1.png    # 单个角色精灵
│   ├── background-*.png   # 背景图片
│   ├── button*.png        # 按钮图片
│   └── ...               # 其他游戏图片
├── media/                 # 音频文件
│   ├── *.m4a             # 音频文件（M4A格式）
│   └── *.ogg             # 音频文件（OGG格式）
└── icons/                # 应用图标
    ├── icon-16.png
    ├── icon-32.png
    └── ...
```

## 🛠️ 工具文件夹
```
├── sprite-tools/          # 精灵图工具集
│   ├── README.md          # 工具使用说明
│   ├── sprite_adjuster_fixed.html  # 可视化调整工具
│   ├── apply_coordinates.py        # 坐标应用工具
│   ├── diagnose_sprites.py         # 诊断工具
│   └── ...               # 其他辅助工具
```

## 📦 备份文件夹
```
├── backup_sprites/        # 自动备份
│   ├── c2runtime_original.js      # 原始运行时文件
│   ├── c2runtime_before_*.js      # 修改前备份
│   └── fish-sheet1_*.png          # 精灵图备份
```

## 📚 文档文件
```
├── README.md              # 项目说明
├── PROJECT_STRUCTURE.md   # 项目结构（本文件）
└── OPTIMIZATION_LOG.md    # 优化日志
```

## 🎯 重要说明

### 核心文件（不可删除）
- `index.html` - 游戏入口
- `c2runtime.js` - 游戏引擎
- `images/` - 所有游戏资源
- `media/` - 音频文件

### 工具文件（可选）
- `sprite-tools/` - 精灵图调整工具
- `backup_sprites/` - 备份文件

### 临时文件（已清理）
- `debug_sprite_*.png` - 调试图片
- `test_*.html` - 测试页面
- `*_verification.png` - 验证图片

## 🚀 部署说明

部署时只需要以下文件：
```
├── index.html
├── c2runtime.js
├── offline.appcache
├── *.css
├── *.js (jQuery等)
├── images/
├── media/
└── icons/
```

工具文件夹和备份文件夹可以不包含在部署中。
