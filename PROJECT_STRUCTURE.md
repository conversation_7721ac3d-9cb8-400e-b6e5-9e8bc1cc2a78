# 鱼类世界 - Match 3 游戏项目结构文档

## 📋 项目概述

这是一个基于 HTML5 Canvas 和 Construct 2 引擎开发的三消类游戏，具有完整的离线缓存、音频处理、性能优化等功能。

## 📁 项目结构

```
鱼类世界-MatCH3/
├── 📄 核心文件
│   ├── index.html              # 主游戏页面
│   ├── c2runtime.js           # Construct 2 运行时引擎
│   ├── app.manifest           # 应用清单文件
│   └── offline.appcache       # 离线缓存清单
│
├── 🎨 样式文件
│   ├── main.css               # 主样式文件
│   ├── style.css              # 优化后的游戏样式
│   ├── page_achievements.css  # 成就页面样式
│   └── page_leaderboards.css  # 排行榜页面样式
│
├── 📚 JavaScript 库
│   ├── jquery-2.0.0.min.js    # jQuery 核心库
│   ├── jquery-1.8.3.min.js    # jQuery 备用版本
│   └── jquery.equalheights.js # jQuery 等高插件
│
├── 🖼️ 图像资源 (images/)
│   ├── 游戏背景
│   │   └── background-sheet0.png
│   ├── 游戏元素
│   │   ├── fish-sheet0.png     # 鱼类精灵图 1
│   │   ├── fish-sheet1.png     # 鱼类精灵图 2
│   │   ├── chessmap-sheet0.png # 游戏棋盘
│   │   └── boom-sheet0.png     # 爆炸效果
│   ├── UI 界面
│   │   ├── buttonplay-sheet0.png    # 开始按钮
│   │   ├── btnpause-sheet0.png      # 暂停按钮
│   │   ├── btnmelody-sheet0.png     # 音乐按钮
│   │   ├── btnsound-sheet0.png      # 音效按钮
│   │   └── btnreturn-sheet0.png     # 返回按钮
│   ├── 游戏状态
│   │   ├── scoregui-sheet0.png      # 分数界面
│   │   ├── timergui-sheet0.png      # 计时器界面
│   │   ├── progressgui-sheet0.png   # 进度条界面
│   │   └── bestscoregui-sheet0.png  # 最佳分数界面
│   ├── 特效元素
│   │   ├── ministar_01-sheet0.png   # 星星特效 1
│   │   ├── ministar_02-sheet0.png   # 星星特效 2
│   │   ├── ministar_03-sheet0.png   # 星星特效 3
│   │   ├── ministar_04-sheet0.png   # 星星特效 4
│   │   ├── ministar_06-sheet0.png   # 星星特效 6
│   │   ├── ministar_07-sheet0.png   # 星星特效 7
│   │   ├── ministar_08-sheet0.png   # 星星特效 8
│   │   └── ministar_10-sheet0.png   # 星星特效 10
│   ├── 交互元素
│   │   ├── cursor-sheet0.png        # 鼠标指针
│   │   ├── cursorhover-sheet0.png   # 悬停指针
│   │   ├── selection-sheet0.png     # 选择框 1
│   │   ├── selection2-sheet0.png    # 选择框 2
│   │   ├── selector-sheet0.png      # 选择器 1
│   │   ├── selector2-sheet0.png     # 选择器 2
│   │   └── marker-sheet0.png        # 标记
│   ├── 加载界面
│   │   ├── loading-sheet0.png           # 加载背景
│   │   ├── loadingbar-sheet0.png        # 加载条
│   │   ├── loadingbarbackground-sheet0.png # 加载条背景
│   │   └── loadingfont.png              # 加载字体
│   ├── 字体资源
│   │   ├── scorefont.png            # 分数字体
│   │   ├── bestscorefont.png        # 最佳分数字体
│   │   └── lastscorefont.png        # 上次分数字体
│   └── 开始菜单
│       ├── fishstartmenu-sheet0.png     # 开始菜单鱼 1
│       ├── fishstartmenu2-sheet0.png    # 开始菜单鱼 2
│       ├── fishstartmenu3-sheet0.png    # 开始菜单鱼 3
│       ├── fishstartmenu4-sheet0.png    # 开始菜单鱼 4
│       ├── fishstartmenu5-sheet0.png    # 开始菜单鱼 5
│       └── fishstartmenu6-sheet0.png    # 开始菜单鱼 6
│
├── 🔊 音频资源 (media/)
│   ├── gamemusic.m4a          # 背景音乐 (M4A)
│   ├── gamemusic.ogg          # 背景音乐 (OGG)
│   ├── button.m4a             # 按钮音效 (M4A)
│   ├── button.ogg             # 按钮音效 (OGG)
│   ├── match.m4a              # 匹配音效 (M4A)
│   ├── match.ogg              # 匹配音效 (OGG)
│   ├── score.m4a              # 得分音效 (M4A)
│   ├── score.ogg              # 得分音效 (OGG)
│   ├── touchblock.m4a         # 触摸阻挡音效 (M4A)
│   └── touchblock.ogg         # 触摸阻挡音效 (OGG)
│
├── 🖼️ 应用图标
│   ├── icon-16.png            # 16x16 图标
│   ├── icon-32.png            # 32x32 图标
│   ├── icon-114.png           # 114x114 图标
│   ├── icon-128.png           # 128x128 图标
│   └── icon-256.png           # 256x256 图标
│
├── 🔧 其他资源
│   ├── loading-logo.png       # 加载 Logo
│   ├── loading.gif            # 加载动画
│   ├── close.png              # 关闭按钮
│   └── achievement_locked.png # 锁定成就图标
│
└── 📖 文档文件
    ├── README.md              # 项目说明文档
    ├── OPTIMIZATION_LOG.md    # 优化日志
    └── PROJECT_STRUCTURE.md   # 项目结构文档 (本文件)
```

## 📄 核心文件详细说明

### 🎮 index.html
**功能**: 游戏主页面，包含完整的游戏运行环境

**主要特性**:
- 响应式 Canvas 画布管理
- 音频上下文解锁机制
- 性能监控 (FPS、内存使用)
- 游戏暂停/恢复逻辑
- 移动设备优化
- 错误处理和日志记录

**关键功能模块**:
```javascript
// 音频解锁
function unlockAudio() { /* 处理移动设备音频权限 */ }

// Canvas 自适应
function resizeCanvas() { /* 响应式画布调整 */ }

// 性能监控
function updateFPS() { /* FPS 和性能统计 */ }

// 游戏状态管理
function suspendGame() { /* 游戏暂停 */ }
function resumeGame() { /* 游戏恢复 */ }
```

### ⚙️ c2runtime.js
**功能**: Construct 2 游戏引擎运行时

**主要职责**:
- 游戏逻辑执行
- 渲染管理
- 事件处理
- 物理引擎
- 音频播放控制
- 资源加载管理

### 📱 app.manifest
**功能**: Web 应用清单文件

**配置内容**:
- 应用基本信息
- 图标配置
- 显示模式设置
- 主题颜色定义

### 💾 offline.appcache
**功能**: 应用缓存清单，实现离线游戏功能

**缓存策略**:
- **CACHE**: 明确缓存的资源列表
- **NETWORK**: 需要网络访问的资源
- **FALLBACK**: 离线时的备用页面

## 🎨 样式文件说明

### 🖌️ style.css
**功能**: 主要游戏样式，包含性能优化

**优化特性**:
- Canvas 硬件加速
- 移动设备触摸优化
- 高 DPI 显示支持
- 防缩放设置

### 🎯 main.css
**功能**: 基础样式导入和布局

### 🏆 page_achievements.css
**功能**: 成就系统页面样式

### 📊 page_leaderboards.css
**功能**: 排行榜页面样式

## 📚 JavaScript 库说明

### 📖 jquery-2.0.0.min.js
**功能**: jQuery 核心库
- DOM 操作
- 事件处理
- AJAX 请求
- 动画效果

### 📏 jquery.equalheights.js
**功能**: jQuery 等高插件
- 元素高度均衡
- 响应式布局支持

## 🎵 音频资源说明

### 🎼 音频格式策略
项目采用双格式音频策略确保跨浏览器兼容性:
- **M4A**: 适用于 Safari、iOS 设备
- **OGG**: 适用于 Firefox、Chrome

### 🔊 音频文件用途
- `gamemusic.*`: 背景音乐循环播放
- `button.*`: UI 按钮点击反馈
- `match.*`: 三消匹配成功音效
- `score.*`: 得分提示音效
- `touchblock.*`: 无效操作提示音效

## 🖼️ 图像资源组织

### 🎮 游戏元素
- **鱼类精灵**: 主要游戏对象，支持动画
- **特效系统**: 多种星星特效，增强视觉体验
- **UI 组件**: 完整的用户界面元素

### 📱 图标系统
提供多尺寸图标支持不同设备:
- 16px: 浏览器标签页
- 32px: 桌面快捷方式
- 114px: iOS 设备
- 128px: Chrome 应用
- 256px: 高分辨率显示

## 🔧 技术特性

### 🚀 性能优化
- Canvas 硬件加速
- 资源预加载
- FPS 监控
- 内存管理
- 事件防抖

### 📱 移动端适配
- 触摸事件优化
- 响应式布局
- 音频权限处理
- 防缩放设置

### 🌐 离线支持
- Application Cache
- 资源本地缓存
- 离线游戏体验

### 🔊 音频处理
- Web Audio API
- 跨浏览器兼容
- 用户交互解锁
- 音频上下文管理

## 🛠️ 开发和维护

### 📝 文档文件
- `README.md`: 项目介绍和使用说明
- `OPTIMIZATION_LOG.md`: 性能优化记录
- `PROJECT_STRUCTURE.md`: 项目结构说明 (本文件)

### 🔍 调试和监控
- 浏览器控制台日志
- 性能指标监控
- 错误捕获和报告
- FPS 实时显示

## 🎯 游戏特色

1. **完整的三消游戏机制**
2. **精美的海洋主题视觉设计**
3. **流畅的动画和特效系统**
4. **完善的音频反馈**
5. **跨平台兼容性**
6. **离线游戏支持**
7. **性能优化和监控**

---

*本文档描述了鱼类世界 Match 3 游戏的完整项目结构，为开发者提供了详细的文件功能说明和技术实现参考。*