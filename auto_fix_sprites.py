#!/usr/bin/env python3
"""
自动修复精灵图布局
分析实际的精灵图内容并生成正确的坐标
"""

from PIL import Image, ImageDraw
import numpy as np

def analyze_actual_layout():
    """分析实际的精灵图布局"""
    try:
        img = Image.open('images/fish-sheet0.png')
        print(f"🔍 分析精灵图: {img.size}")
        
        # 转换为numpy数组以便分析
        img_array = np.array(img)
        
        # 如果是RGBA，检查alpha通道来找到非透明区域
        if img.mode == 'RGBA':
            alpha = img_array[:, :, 3]
            # 找到有内容的区域
            non_transparent = alpha > 0
        else:
            # 如果是RGB，假设白色为透明
            gray = np.mean(img_array, axis=2)
            non_transparent = gray < 250
        
        # 找到有内容的行和列
        rows_with_content = np.any(non_transparent, axis=1)
        cols_with_content = np.any(non_transparent, axis=0)
        
        # 找到内容区域的边界
        content_rows = np.where(rows_with_content)[0]
        content_cols = np.where(cols_with_content)[0]
        
        if len(content_rows) == 0 or len(content_cols) == 0:
            print("❌ 未找到任何内容")
            return None
        
        top = content_rows[0]
        bottom = content_rows[-1]
        left = content_cols[0]
        right = content_cols[-1]
        
        print(f"📊 内容区域: ({left}, {top}) 到 ({right}, {bottom})")
        print(f"📏 内容尺寸: {right - left + 1} × {bottom - top + 1}")
        
        return analyze_grid_layout(img, left, top, right, bottom)
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def analyze_grid_layout(img, left, top, right, bottom):
    """分析网格布局"""
    content_width = right - left + 1
    content_height = bottom - top + 1
    
    print(f"\n🔍 分析网格布局...")
    
    # 尝试不同的网格配置
    possible_grids = [
        (3, 3, "3×3网格"),
        (4, 3, "4×3网格"), 
        (3, 4, "3×4网格"),
        (4, 4, "4×4网格")
    ]
    
    best_grid = None
    best_score = 0
    
    for cols, rows, desc in possible_grids:
        cell_width = content_width // cols
        cell_height = content_height // rows
        
        print(f"📐 尝试 {desc}: 每格 {cell_width}×{cell_height}")
        
        # 检查这个网格配置的合理性
        score = 0
        sprites_found = []
        
        for row in range(rows):
            for col in range(cols):
                x = left + col * cell_width
                y = top + row * cell_height
                
                # 检查这个位置是否有内容
                cell_img = img.crop((x, y, x + cell_width, y + cell_height))
                bbox = cell_img.getbbox()
                
                if bbox is not None:
                    score += 1
                    sprites_found.append((row * cols + col + 1, x, y, cell_width, cell_height))
        
        print(f"   找到 {score} 个精灵")
        
        if score > best_score:
            best_score = score
            best_grid = (cols, rows, desc, sprites_found, cell_width, cell_height)
    
    return best_grid

def create_optimized_coordinates(grid_info):
    """创建优化的坐标"""
    if not grid_info:
        return None
    
    cols, rows, desc, sprites_found, cell_width, cell_height = grid_info
    
    print(f"\n🎯 最佳布局: {desc}")
    print(f"📏 精灵尺寸: {cell_width}×{cell_height}")
    print(f"🔢 找到精灵数量: {len(sprites_found)}")
    
    print(f"\n📍 优化后的坐标:")
    coordinates = []
    for sprite_num, x, y, w, h in sprites_found:
        print(f"精灵 {sprite_num}: ({x}, {y}, {w}, {h})")
        coordinates.append((x, y, w, h))
    
    return coordinates

def update_runtime_with_new_coords(coordinates):
    """使用新坐标更新运行时文件"""
    if not coordinates:
        print("❌ 没有坐标可更新")
        return False
    
    try:
        # 备份原文件
        import shutil
        shutil.copy('c2runtime.js', 'backup_sprites/c2runtime_before_auto_fix.js')
        
        with open('c2runtime.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 找到所有fish-sheet0.png的引用
        import re
        pattern = r'(\["images/fish-sheet0\.png",\s*\d+,\s*)(\d+),\s*(\d+),\s*(\d+),\s*(\d+)(\s*,\s*[^\]]+\])'
        
        matches = list(re.finditer(pattern, content))
        print(f"\n🔍 找到 {len(matches)} 个需要更新的坐标")
        
        # 更新坐标
        updated_content = content
        for i, match in enumerate(matches):
            if i < len(coordinates):
                x, y, w, h = coordinates[i]
                new_coords = f"{match.group(1)}{x}, {y}, {w}, {h}{match.group(6)}"
                updated_content = updated_content.replace(match.group(0), new_coords)
                print(f"✅ 更新精灵 {i+1}: ({x}, {y}, {w}, {h})")
            else:
                print(f"⚠️ 精灵 {i+1}: 超出坐标范围，保持原样")
        
        # 保存更新后的文件
        with open('c2runtime.js', 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print(f"✅ 已更新运行时文件")
        return True
        
    except Exception as e:
        print(f"❌ 更新运行时文件失败: {e}")
        return False

def create_visual_verification():
    """创建可视化验证图"""
    try:
        img = Image.open('images/fish-sheet0.png')
        verify_img = img.copy()
        draw = ImageDraw.Draw(verify_img)
        
        # 重新分析布局
        grid_info = analyze_actual_layout()
        if not grid_info:
            return
        
        cols, rows, desc, sprites_found, cell_width, cell_height = grid_info
        
        # 绘制网格和标签
        for sprite_num, x, y, w, h in sprites_found:
            # 绘制边框
            draw.rectangle([x, y, x + w - 1, y + h - 1], 
                         outline=(255, 0, 0, 255), width=2)
            
            # 绘制精灵编号
            draw.text((x + 5, y + 5), f"精灵 {sprite_num}", 
                     fill=(255, 255, 0, 255))
        
        verify_img.save('auto_fix_verification.png')
        print("✅ 已创建验证图: auto_fix_verification.png")
        
    except Exception as e:
        print(f"❌ 创建验证图失败: {e}")

if __name__ == "__main__":
    print("🔧 开始自动修复精灵图布局...")
    print("=" * 50)
    
    # 分析实际布局
    grid_info = analyze_actual_layout()
    
    if grid_info:
        # 创建优化坐标
        coordinates = create_optimized_coordinates(grid_info)
        
        if coordinates:
            # 更新运行时文件
            if update_runtime_with_new_coords(coordinates):
                print("\n🎉 自动修复完成！")
                
                # 创建验证图
                create_visual_verification()
                
                print("\n📋 下一步:")
                print("1. 查看 auto_fix_verification.png 验证布局")
                print("2. 更新缓存版本")
                print("3. 刷新游戏测试")
            else:
                print("\n❌ 自动修复失败")
        else:
            print("\n❌ 无法生成坐标")
    else:
        print("\n❌ 无法分析布局")
