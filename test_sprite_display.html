<!DOCTYPE html>
<html>
<head>
    <title>精灵图显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .sprite-container {
            display: inline-block;
            margin: 10px;
            border: 2px solid #333;
            background: white;
            text-align: center;
            padding: 10px;
        }
        .sprite {
            display: block;
            border: 1px solid #ccc;
        }
        .sprite-info {
            margin-top: 5px;
            font-size: 12px;
            color: #666;
        }
        .grid-container {
            margin: 20px 0;
            text-align: center;
        }
        .debug-image {
            max-width: 100%;
            border: 2px solid #333;
        }
    </style>
</head>
<body>
    <h1>🔍 精灵图显示测试</h1>
    
    <h2>📋 各个精灵 (从 fish-sheet0.png 提取)</h2>
    <div id="sprites-container"></div>
    
    <h2>🎯 调试叠加图</h2>
    <div class="grid-container">
        <img src="debug_sprite_overlay.png" alt="调试叠加图" class="debug-image">
    </div>
    
    <h2>📊 完整精灵图</h2>
    <div class="grid-container">
        <img src="images/fish-sheet0.png" alt="完整精灵图" class="debug-image">
    </div>

    <script>
        // 精灵坐标信息
        const sprites = [
            {id: 1, x: 0, y: 0, width: 258, height: 311},
            {id: 2, x: 258, y: 0, width: 258, height: 311},
            {id: 3, x: 516, y: 0, width: 258, height: 311},
            {id: 4, x: 0, y: 311, width: 258, height: 311},
            {id: 5, x: 258, y: 311, width: 258, height: 311},
            {id: 6, x: 516, y: 311, width: 258, height: 311},
            {id: 7, x: 0, y: 622, width: 258, height: 311},
            {id: 8, x: 258, y: 622, width: 258, height: 311},
            {id: 9, x: 516, y: 622, width: 258, height: 311}
        ];

        // 创建精灵显示
        const container = document.getElementById('sprites-container');
        
        sprites.forEach(sprite => {
            const div = document.createElement('div');
            div.className = 'sprite-container';
            
            // 检查是否有对应的调试文件
            const img = document.createElement('img');
            img.src = `debug_sprite_${sprite.id}.png`;
            img.alt = `精灵 ${sprite.id}`;
            img.className = 'sprite';
            img.style.width = '129px'; // 缩放50%以便显示
            img.style.height = '155px';
            
            const info = document.createElement('div');
            info.className = 'sprite-info';
            info.innerHTML = `
                精灵 ${sprite.id}<br>
                坐标: (${sprite.x}, ${sprite.y})<br>
                尺寸: ${sprite.width}×${sprite.height}
            `;
            
            div.appendChild(img);
            div.appendChild(info);
            container.appendChild(div);
            
            // 检查图片是否加载成功
            img.onload = function() {
                console.log(`✅ 精灵 ${sprite.id} 加载成功`);
            };
            
            img.onerror = function() {
                console.log(`❌ 精灵 ${sprite.id} 加载失败`);
                img.style.background = '#ffcccc';
                img.alt = `精灵 ${sprite.id} - 加载失败`;
            };
        });
        
        // 页面加载完成后的检查
        window.onload = function() {
            console.log('🔍 精灵图测试页面加载完成');
            console.log('请检查上方显示的各个精灵是否正确');
        };
    </script>
</body>
</html>
